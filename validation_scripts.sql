-- =====================================================
-- SCRIPTS DE VALIDAÇÃO PÓS-MIGRAÇÃO - SERVICETECH
-- =====================================================

-- =====================================================
-- 1. VALIDAÇÃO DE ESTRUTURA
-- =====================================================

-- Verificar se todas as novas tabelas foram criadas
SELECT 
    table_name,
    CASE 
        WHEN table_name IN ('roles', 'user_roles', 'convites_colaborador') THEN 'NOVA TABELA'
        ELSE 'TABELA EXISTENTE'
    END as status
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;

-- Verificar se todas as colunas foram adicionadas
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name IN ('empresas', 'colaboradores_empresa', 'agendamentos', 'servicos', 'notificacoes')
AND column_name IN (
    'website', 'instagram', 'whatsapp', 'email_contato', 'configuracao_notificacoes',
    'configuracao_agendamento', 'dias_funcionamento', 'timezone', 'papel_empresa',
    'data_inicio_trabalho', 'data_fim_trabalho', 'observacoes_internas',
    'tipo_agendamento', 'combo_id', 'assinatura_cliente_id', 'observacoes_empresa',
    'avaliacao_cliente', 'comentario_avaliacao', 'data_avaliacao', 'imagem_url',
    'tags', 'ordem_exibicao', 'disponivel_online', 'preparacao_minutos',
    'limpeza_minutos', 'template_id', 'parametros_template', 'prioridade',
    'agrupamento_id', 'acao_url', 'expira_em'
)
ORDER BY table_name, column_name;

-- Verificar se índices foram criados
SELECT 
    indexname,
    tablename,
    indexdef
FROM pg_indexes 
WHERE schemaname = 'public' 
AND indexname LIKE 'idx_%'
ORDER BY tablename, indexname;

-- =====================================================
-- 2. VALIDAÇÃO DE DADOS
-- =====================================================

-- Verificar contagem de registros antes e depois
SELECT 'agendamentos' as tabela, COUNT(*) as total FROM agendamentos
UNION ALL
SELECT 'empresas' as tabela, COUNT(*) as total FROM empresas
UNION ALL
SELECT 'colaboradores_empresa' as tabela, COUNT(*) as total FROM colaboradores_empresa
UNION ALL
SELECT 'servicos' as tabela, COUNT(*) as total FROM servicos
UNION ALL
SELECT 'notificacoes' as tabela, COUNT(*) as total FROM notificacoes
UNION ALL
SELECT 'pagamentos' as tabela, COUNT(*) as total FROM pagamentos
UNION ALL
SELECT 'user_roles' as tabela, COUNT(*) as total FROM user_roles
UNION ALL
SELECT 'roles' as tabela, COUNT(*) as total FROM roles
ORDER BY tabela;

-- Verificar se papéis foram migrados corretamente
SELECT 
    r.nome_papel,
    COUNT(ur.user_role_id) as usuarios_com_papel,
    COUNT(CASE WHEN ur.empresa_id IS NOT NULL THEN 1 END) as com_empresa,
    COUNT(CASE WHEN ur.empresa_id IS NULL THEN 1 END) as sem_empresa
FROM roles r
LEFT JOIN user_roles ur ON r.role_id = ur.role_id AND ur.ativo = true
GROUP BY r.role_id, r.nome_papel
ORDER BY r.nome_papel;

-- Verificar integridade referencial
SELECT 'agendamentos_empresa' as verificacao, COUNT(*) as problemas
FROM agendamentos a 
LEFT JOIN empresas e ON a.empresa_id = e.empresa_id 
WHERE e.empresa_id IS NULL

UNION ALL

SELECT 'agendamentos_cliente' as verificacao, COUNT(*) as problemas
FROM agendamentos a 
LEFT JOIN auth.users u ON a.cliente_user_id = u.id 
WHERE u.id IS NULL

UNION ALL

SELECT 'colaboradores_empresa' as verificacao, COUNT(*) as problemas
FROM colaboradores_empresa ce 
LEFT JOIN empresas e ON ce.empresa_id = e.empresa_id 
WHERE e.empresa_id IS NULL

UNION ALL

SELECT 'servicos_empresa' as verificacao, COUNT(*) as problemas
FROM servicos s 
LEFT JOIN empresas e ON s.empresa_id = e.empresa_id 
WHERE e.empresa_id IS NULL;

-- =====================================================
-- 3. VALIDAÇÃO DE POLÍTICAS RLS
-- =====================================================

-- Verificar se RLS está habilitado nas tabelas
SELECT 
    schemaname,
    tablename,
    rowsecurity as rls_habilitado
FROM pg_tables 
WHERE schemaname = 'public' 
AND tablename IN ('roles', 'user_roles', 'convites_colaborador', 'agendamentos', 'empresas', 'servicos')
ORDER BY tablename;

-- Verificar políticas RLS criadas
SELECT 
    schemaname,
    tablename,
    policyname,
    permissive,
    cmd
FROM pg_policies 
WHERE schemaname = 'public'
ORDER BY tablename, policyname;

-- =====================================================
-- 4. VALIDAÇÃO DE FUNÇÕES
-- =====================================================

-- Verificar se funções foram criadas
SELECT 
    routine_name,
    routine_type,
    data_type as return_type
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
    'verificar_limite_colaboradores',
    'update_updated_at_column',
    'get_user_role_in_company',
    'user_has_permission_in_company'
)
ORDER BY routine_name;

-- Testar função de verificação de papel
SELECT 
    'Teste função get_user_role_in_company' as teste,
    get_user_role_in_company(
        (SELECT id FROM auth.users LIMIT 1),
        (SELECT empresa_id FROM empresas LIMIT 1)
    ) as resultado;

-- =====================================================
-- 5. TESTES DE PERFORMANCE
-- =====================================================

-- Testar performance de queries principais
EXPLAIN ANALYZE 
SELECT a.*, e.nome_empresa, s.nome_servico 
FROM agendamentos a
JOIN empresas e ON a.empresa_id = e.empresa_id
JOIN servicos s ON a.servico_id = s.servico_id
WHERE a.data_hora_inicio >= CURRENT_DATE
ORDER BY a.data_hora_inicio
LIMIT 100;

EXPLAIN ANALYZE
SELECT * FROM user_roles ur
JOIN roles r ON ur.role_id = r.role_id
WHERE ur.user_id = (SELECT id FROM auth.users LIMIT 1);

-- =====================================================
-- 6. VALIDAÇÃO DE TRIGGERS
-- =====================================================

-- Verificar se triggers foram criados
SELECT 
    trigger_name,
    event_manipulation,
    event_object_table,
    action_timing
FROM information_schema.triggers 
WHERE trigger_schema = 'public'
ORDER BY event_object_table, trigger_name;

-- =====================================================
-- 7. TESTES FUNCIONAIS BÁSICOS
-- =====================================================

-- Teste 1: Criar um papel de usuário (deve funcionar)
DO $$
DECLARE
    test_user_id UUID;
    test_empresa_id INTEGER;
    role_id INTEGER;
BEGIN
    -- Buscar um usuário e empresa para teste
    SELECT id INTO test_user_id FROM auth.users LIMIT 1;
    SELECT empresa_id INTO test_empresa_id FROM empresas LIMIT 1;
    SELECT role_id INTO role_id FROM roles WHERE nome_papel = 'Usuário';
    
    -- Tentar inserir papel (deve funcionar)
    INSERT INTO user_roles (user_id, role_id, empresa_id, ativo)
    VALUES (test_user_id, role_id, NULL, true)
    ON CONFLICT DO NOTHING;
    
    RAISE NOTICE 'Teste 1 - Criação de papel: SUCESSO';
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Teste 1 - Criação de papel: FALHA - %', SQLERRM;
END $$;

-- Teste 2: Verificar limite de colaboradores
DO $$
DECLARE
    test_empresa_id INTEGER;
    limite_atual INTEGER;
BEGIN
    SELECT empresa_id INTO test_empresa_id FROM empresas LIMIT 1;
    
    -- Contar colaboradores atuais
    SELECT COUNT(*) INTO limite_atual
    FROM colaboradores_empresa 
    WHERE empresa_id = test_empresa_id AND ativo = true;
    
    RAISE NOTICE 'Teste 2 - Empresa % tem % colaboradores', test_empresa_id, limite_atual;
EXCEPTION
    WHEN OTHERS THEN
        RAISE NOTICE 'Teste 2 - Verificação de limite: FALHA - %', SQLERRM;
END $$;

-- =====================================================
-- 8. RELATÓRIO FINAL DE VALIDAÇÃO
-- =====================================================

-- Resumo da migração
SELECT 
    'RESUMO DA MIGRAÇÃO' as secao,
    'Tabelas criadas' as item,
    COUNT(*) as valor
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('roles', 'user_roles', 'convites_colaborador')

UNION ALL

SELECT 
    'RESUMO DA MIGRAÇÃO' as secao,
    'Políticas RLS criadas' as item,
    COUNT(*) as valor
FROM pg_policies 
WHERE schemaname = 'public'
AND policyname LIKE '%user_roles%' OR policyname LIKE '%roles%' OR policyname LIKE '%convites%'

UNION ALL

SELECT 
    'RESUMO DA MIGRAÇÃO' as secao,
    'Funções criadas' as item,
    COUNT(*) as valor
FROM information_schema.routines 
WHERE routine_schema = 'public' 
AND routine_name IN (
    'verificar_limite_colaboradores',
    'update_updated_at_column',
    'get_user_role_in_company',
    'user_has_permission_in_company'
)

UNION ALL

SELECT 
    'RESUMO DA MIGRAÇÃO' as secao,
    'Usuários com papéis' as item,
    COUNT(*) as valor
FROM user_roles 
WHERE ativo = true

ORDER BY secao, item;
