// Tipos aprimorados para integração com Supabase

// Tipos base do banco de dados
export interface Database {
  public: {
    Tables: {
      empresas: {
        Row: Empresa;
        Insert: EmpresaInsert;
        Update: EmpresaUpdate;
      };
      servicos: {
        Row: Servico;
        Insert: ServicoInsert;
        Update: ServicoUpdate;
      };
      agendamentos: {
        Row: Agendamento;
        Insert: AgendamentoInsert;
        Update: AgendamentoUpdate;
      };
      colaboradores_empresa: {
        Row: ColaboradorEmpresa;
        Insert: ColaboradorEmpresaInsert;
        Update: ColaboradorEmpresaUpdate;
      };
      user_roles: {
        Row: UserRole;
        Insert: UserRoleInsert;
        Update: UserRoleUpdate;
      };
      planos_saas: {
        Row: PlanoSaas;
        Insert: PlanoSaasInsert;
        Update: PlanoSaasUpdate;
      };
    };
    Views: {
      view_dashboard_metricas: {
        Row: DashboardMetricas;
      };
      view_agendamentos_detalhados: {
        Row: AgendamentoDetalhado;
      };
    };
    Functions: {
      get_empresa_metricas: {
        Args: { empresa_id: number };
        Returns: EmpresaMetricas;
      };
      get_dashboard_data: {
        Args: { user_id: string };
        Returns: DashboardData;
      };
    };
  };
}

// Entidades principais
export interface Empresa {
  empresa_id: number;
  nome_empresa: string;
  cnpj?: string;
  endereco_completo?: string;
  telefone?: string;
  email?: string;
  segmento?: string;
  descricao?: string;
  logo_url?: string;
  imagem_capa_url?: string;
  slug: string;
  status: 'ativo' | 'inativo' | 'pendente' | 'suspenso';
  horario_funcionamento?: HorarioFuncionamento;
  stripe_customer_id?: string;
  stripe_account_id?: string;
  politica_cancelamento?: PoliticaCancelamento;
  configuracoes?: EmpresaConfiguracoes;
  created_at: string;
  updated_at: string;
}

export interface EmpresaInsert extends Omit<Empresa, 'empresa_id' | 'created_at' | 'updated_at'> {
  empresa_id?: never;
}

export interface EmpresaUpdate extends Partial<Omit<Empresa, 'empresa_id' | 'created_at'>> {}

export interface Servico {
  servico_id: number;
  empresa_id: number;
  nome_servico: string;
  descricao?: string;
  duracao_minutos: number;
  preco: number;
  categoria?: string;
  ativo: boolean;
  ordem_exibicao?: number;
  configuracoes?: ServicoConfiguracoes;
  created_at: string;
  updated_at: string;
}

export interface ServicoInsert extends Omit<Servico, 'servico_id' | 'created_at' | 'updated_at'> {
  servico_id?: never;
}

export interface ServicoUpdate extends Partial<Omit<Servico, 'servico_id' | 'created_at'>> {}

export interface Agendamento {
  agendamento_id: number;
  empresa_id: number;
  servico_id: number;
  colaborador_id?: number;
  cliente_id?: string;
  data_agendamento: string;
  hora_inicio: string;
  hora_fim: string;
  status: AgendamentoStatus;
  valor_total: number;
  observacoes?: string;
  observacoes_empresa?: string;
  tipo_agendamento: 'servico' | 'combo' | 'assinatura';
  combo_id?: number;
  assinatura_cliente_id?: number;
  avaliacao_cliente?: number;
  comentario_avaliacao?: string;
  data_avaliacao?: string;
  created_at: string;
  updated_at: string;
}

export interface AgendamentoInsert extends Omit<Agendamento, 'agendamento_id' | 'created_at' | 'updated_at'> {
  agendamento_id?: never;
}

export interface AgendamentoUpdate extends Partial<Omit<Agendamento, 'agendamento_id' | 'created_at'>> {}

export interface ColaboradorEmpresa {
  colaborador_id: number;
  empresa_id: number;
  user_id: string;
  ativo: boolean;
  data_admissao: string;
  data_demissao?: string;
  horarios_trabalho_individual?: HorarioTrabalho;
  configuracoes_financeiras?: ConfiguracoesFinanceiras;
  permissoes?: ColaboradorPermissoes;
  created_at: string;
  updated_at: string;
}

export interface ColaboradorEmpresaInsert extends Omit<ColaboradorEmpresa, 'colaborador_id' | 'created_at' | 'updated_at'> {
  colaborador_id?: never;
}

export interface ColaboradorEmpresaUpdate extends Partial<Omit<ColaboradorEmpresa, 'colaborador_id' | 'created_at'>> {}

export interface UserRole {
  user_role_id: number;
  user_id: string;
  role_id: number;
  empresa_id?: number;
  ativo: boolean;
  data_atribuicao: string;
  data_revogacao?: string;
  atribuido_por: string;
  created_at: string;
  updated_at: string;
}

export interface UserRoleInsert extends Omit<UserRole, 'user_role_id' | 'created_at' | 'updated_at'> {
  user_role_id?: never;
}

export interface UserRoleUpdate extends Partial<Omit<UserRole, 'user_role_id' | 'created_at'>> {}

export interface PlanoSaas {
  plano_id: number;
  nome_plano: string;
  descricao?: string;
  preco_mensal: number;
  preco_anual?: number;
  limite_servicos: number;
  limite_colaboradores: number;
  recursos: PlanoRecursos;
  ativo: boolean;
  ordem_exibicao: number;
  created_at: string;
  updated_at: string;
}

export interface PlanoSaasInsert extends Omit<PlanoSaas, 'plano_id' | 'created_at' | 'updated_at'> {
  plano_id?: never;
}

export interface PlanoSaasUpdate extends Partial<Omit<PlanoSaas, 'plano_id' | 'created_at'>> {}

// Tipos auxiliares
export type AgendamentoStatus = 
  | 'pendente' 
  | 'confirmado' 
  | 'em_andamento' 
  | 'concluido' 
  | 'cancelado' 
  | 'nao_compareceu';

export interface HorarioFuncionamento {
  [key: string]: {
    aberto: boolean;
    abertura?: string;
    fechamento?: string;
    intervalo_inicio?: string;
    intervalo_fim?: string;
  };
}

export interface PoliticaCancelamento {
  permite_cancelamento: boolean;
  prazo_minimo_horas: number;
  taxa_cancelamento?: number;
  politica_no_show?: {
    ativa: boolean;
    taxa?: number;
  };
}

export interface EmpresaConfiguracoes {
  notificacoes: {
    email: boolean;
    sms: boolean;
    whatsapp: boolean;
  };
  agendamento: {
    antecedencia_minima_horas: number;
    antecedencia_maxima_dias: number;
    permite_reagendamento: boolean;
  };
  pagamento: {
    aceita_dinheiro: boolean;
    aceita_cartao: boolean;
    aceita_pix: boolean;
    taxa_servico?: number;
  };
}

export interface ServicoConfiguracoes {
  permite_agendamento_online: boolean;
  requer_confirmacao: boolean;
  tempo_preparacao_minutos?: number;
  tempo_limpeza_minutos?: number;
  maximo_reagendamentos?: number;
}

export interface HorarioTrabalho {
  [key: string]: {
    trabalha: boolean;
    inicio?: string;
    fim?: string;
    intervalo_inicio?: string;
    intervalo_fim?: string;
  };
}

export interface ConfiguracoesFinanceiras {
  tipo_remuneracao: 'fixo' | 'comissao' | 'misto';
  valor_fixo?: number;
  percentual_comissao?: number;
  meta_mensal?: number;
  custo_por_hora?: number;
}

export interface ColaboradorPermissoes {
  pode_criar_agendamento: boolean;
  pode_editar_agendamento: boolean;
  pode_cancelar_agendamento: boolean;
  pode_ver_financeiro: boolean;
  pode_gerenciar_servicos: boolean;
  pode_ver_relatorios: boolean;
}

export interface PlanoRecursos {
  agendamento_online: boolean;
  notificacoes_automaticas: boolean;
  relatorios_avancados: boolean;
  integracao_pagamento: boolean;
  api_acesso: boolean;
  suporte_prioritario: boolean;
  backup_automatico: boolean;
  multi_localizacao: boolean;
}

// Views e funções
export interface DashboardMetricas {
  empresa_id: number;
  total_agendamentos_mes: number;
  agendamentos_pendentes: number;
  agendamentos_confirmados: number;
  agendamentos_concluidos: number;
  agendamentos_cancelados: number;
  receita_bruta_mes: number;
  receita_liquida_mes: number;
  total_clientes_ativos: number;
  taxa_confirmacao_mes: number;
  taxa_cancelamento_mes: number;
  ticket_medio: number;
  crescimento_mes_anterior: number;
}

export interface AgendamentoDetalhado extends Agendamento {
  empresa_nome: string;
  servico_nome: string;
  colaborador_nome?: string;
  cliente_nome?: string;
  cliente_email?: string;
  cliente_telefone?: string;
}

export interface EmpresaMetricas {
  agendamentos: {
    total_mes: number;
    pendentes: number;
    confirmados: number;
    concluidos: number;
    cancelados: number;
    taxa_confirmacao: number;
    taxa_cancelamento: number;
  };
  financeiro: {
    receita_bruta_mes: number;
    receita_liquida_mes: number;
    ticket_medio: number;
    crescimento_percentual: number;
  };
  clientes: {
    total_ativos: number;
    novos_mes: number;
    retornaram_mes: number;
    taxa_retencao: number;
  };
  servicos: {
    mais_agendado: string;
    menos_agendado: string;
    media_duracao: number;
    total_ativo: number;
  };
}

export interface DashboardData {
  empresa: Empresa;
  metricas: EmpresaMetricas;
  agendamentos_recentes: AgendamentoDetalhado[];
  plano_atual: PlanoSaas;
  uso_plano: {
    servicos_utilizados: number;
    colaboradores_utilizados: number;
    percentual_uso_servicos: number;
    percentual_uso_colaboradores: number;
  };
}

// Tipos para operações
export interface QueryFilter {
  column: string;
  operator: 'eq' | 'neq' | 'gt' | 'gte' | 'lt' | 'lte' | 'like' | 'ilike' | 'in' | 'contains' | 'containedBy';
  value: any;
}

export interface QueryOptions {
  select?: string;
  filters?: QueryFilter[];
  orderBy?: Array<{ column: string; ascending?: boolean }>;
  limit?: number;
  offset?: number;
  cache?: boolean;
  cacheTTL?: number;
}

export interface MutationResult<T> {
  data: T | null;
  error: any;
  success: boolean;
}

// Tipos para realtime
export interface RealtimePayload<T = any> {
  schema: string;
  table: string;
  commit_timestamp: string;
  eventType: 'INSERT' | 'UPDATE' | 'DELETE';
  new: T;
  old: T;
  errors: any[];
}

// Tipos para autenticação
export interface AuthUser {
  id: string;
  email: string;
  user_metadata: {
    name?: string;
    phone?: string;
    avatar_url?: string;
  };
  app_metadata: {
    provider?: string;
    providers?: string[];
  };
  created_at: string;
  updated_at: string;
}

export interface UserProfile {
  id: string;
  email: string;
  name: string;
  phone?: string;
  avatar_url?: string;
  role: string;
  empresa_id?: number;
  empresa_nome?: string;
  permissions: string[];
  created_at: string;
  updated_at: string;
}
