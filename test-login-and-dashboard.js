/**
 * <PERSON>ript para testar login e acesso ao dashboard programaticamente
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Variáveis de ambiente do Supabase não encontradas');
  process.exit(1);
}

// Cliente Supabase (mesmo que o frontend usa)
const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testLoginAndDashboard() {
  try {
    console.log('🧪 TESTE COMPLETO: LOGIN + DASHBOARD');
    console.log('=' .repeat(60));
    console.log('');

    // 1. Fazer login
    console.log('1️⃣ Fazendo login como <PERSON>...');
    
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'teste123456'
    });

    if (loginError) {
      console.log('❌ Erro no login:', loginError.message);
      return;
    }

    console.log('✅ Login bem-sucedido!');
    console.log(`   User ID: ${loginData.user.id}`);
    console.log(`   Email: ${loginData.user.email}`);
    console.log(`   Role: ${loginData.user.user_metadata?.role}`);
    console.log('');

    // 2. Obter sessão
    console.log('2️⃣ Obtendo sessão...');
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      console.log('❌ Erro ao obter sessão:', sessionError?.message || 'Sessão não encontrada');
      return;
    }

    console.log('✅ Sessão obtida com sucesso!');
    console.log(`   Access Token: ${session.access_token.substring(0, 50)}...`);
    console.log('');

    // 3. Testar API com autenticação
    console.log('3️⃣ Testando API com autenticação...');
    
    // Simular requisição como o frontend faria
    const response = await fetch('http://localhost:3000/api/proprietario/dashboard/empresa', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${session.access_token}`,
        // Simular cookies que o navegador enviaria
        'Cookie': `sb-tlbpsdgoklkekoxzmzlo-auth-token=${session.access_token}; sb-tlbpsdgoklkekoxzmzlo-auth-token.0=${session.access_token}; sb-tlbpsdgoklkekoxzmzlo-auth-token.1=${session.refresh_token}`
      }
    });

    const data = await response.json();
    
    console.log('📊 Status da resposta:', response.status);
    console.log('📋 Sucesso:', data.success);
    
    if (response.status === 200 && data.success) {
      console.log('');
      console.log('🎉 SUCESSO TOTAL! Dashboard funcionando!');
      console.log('🏢 Dados da empresa:');
      console.log(`   Nome: ${data.data.empresa?.nome_empresa}`);
      console.log(`   Status: ${data.data.empresa?.status}`);
      console.log(`   ID: ${data.data.empresa?.empresa_id}`);
      console.log('');
      console.log('📈 Métricas:');
      console.log(`   Agendamentos: ${data.data.metricas?.total_agendamentos_mes}`);
      console.log(`   Receita: R$ ${data.data.metricas?.receita_bruta_mes}`);
      console.log(`   Clientes: ${data.data.metricas?.total_clientes_ativos}`);
      
    } else if (response.status === 401) {
      console.log('❌ Ainda há problema de autenticação na API');
      console.log('🔍 Erro:', data.error);
      console.log('');
      console.log('🔧 Possíveis causas:');
      console.log('   - Cookies não sendo enviados corretamente');
      console.log('   - Problema na verificação de autenticação na API');
      console.log('   - Configuração do middleware de autenticação');
      
    } else if (response.status === 500) {
      console.log('❌ Erro interno do servidor');
      console.log('🔍 Erro:', data.error);
      
    } else {
      console.log('❌ Resposta inesperada');
      console.log('🔍 Dados:', JSON.stringify(data, null, 2));
    }

    // 4. Testar acesso direto ao Supabase
    console.log('');
    console.log('4️⃣ Testando acesso direto ao Supabase...');
    
    const { data: empresaData, error: empresaError } = await supabase
      .from('empresas')
      .select('empresa_id, nome_empresa, status')
      .eq('proprietario_user_id', loginData.user.id)
      .single();

    if (empresaError) {
      console.log('❌ Erro ao buscar empresa via Supabase:', empresaError.message);
      console.log('🔍 Código:', empresaError.code);
    } else {
      console.log('✅ Empresa encontrada via Supabase:');
      console.log(`   Nome: ${empresaData.nome_empresa}`);
      console.log(`   Status: ${empresaData.status}`);
      console.log(`   ID: ${empresaData.empresa_id}`);
    }

    // 5. Fazer logout
    console.log('');
    console.log('5️⃣ Fazendo logout...');
    await supabase.auth.signOut();
    console.log('✅ Logout realizado');

  } catch (error) {
    console.error('❌ Erro inesperado:', error.message);
    console.error('Stack:', error.stack);
  }
}

// Executar teste
testLoginAndDashboard();
