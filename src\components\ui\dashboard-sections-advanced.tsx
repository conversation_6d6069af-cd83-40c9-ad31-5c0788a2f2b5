'use client';

import React, { memo, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MagicCard } from './magic-card';
import { TextAnimate } from './text-animate';
import { ShimmerButton } from './shimmer-button';
import { NumberTicker } from './number-ticker';

// Tipos para configuração das seções
interface SectionConfig {
  id: string;
  title: string;
  description: string;
  icon: string;
  gradientColor: string;
  priority: 'high' | 'medium' | 'low';
  category: 'business' | 'technical' | 'administrative';
  refreshInterval?: number;
  collapsible?: boolean;
  defaultExpanded?: boolean;
}

interface SectionAction {
  id: string;
  label: string;
  icon: string;
  variant: 'primary' | 'secondary' | 'danger' | 'success';
  onClick: () => void;
  loading?: boolean;
  disabled?: boolean;
  badge?: string | number;
}

interface SectionMetric {
  label: string;
  value: number | string;
  change?: number;
  trend?: 'up' | 'down' | 'stable';
  format?: 'number' | 'currency' | 'percentage';
  icon?: string;
}

interface AdvancedSectionProps {
  config: SectionConfig;
  actions?: SectionAction[];
  metrics?: SectionMetric[];
  children: React.ReactNode;
  className?: string;
  onRefresh?: () => void;
  loading?: boolean;
  error?: string;
  lastUpdated?: Date;
}

// Componente de métrica individual
const SectionMetricCard = memo(({ 
  metric, 
  delay = 0 
}: { 
  metric: SectionMetric; 
  delay?: number; 
}) => {
  const formatValue = (value: number | string, format?: string) => {
    if (typeof value === 'string') return value;
    
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL'
        }).format(value);
      case 'percentage':
        return `${value.toFixed(1)}%`;
      default:
        return value.toLocaleString('pt-BR');
    }
  };

  const getTrendConfig = () => {
    switch (metric.trend) {
      case 'up':
        return { icon: '📈', color: 'text-green-600', bgColor: 'bg-green-100' };
      case 'down':
        return { icon: '📉', color: 'text-red-600', bgColor: 'bg-red-100' };
      default:
        return { icon: '➡️', color: 'text-gray-600', bgColor: 'bg-gray-100' };
    }
  };

  const trendConfig = getTrendConfig();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay, duration: 0.5 }}
      className="bg-white dark:bg-gray-800 rounded-lg p-4 border border-gray-200 dark:border-gray-700"
    >
      <div className="flex items-center justify-between mb-2">
        {metric.icon && (
          <span className="text-lg">{metric.icon}</span>
        )}
        {metric.change !== undefined && (
          <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${trendConfig.bgColor} ${trendConfig.color}`}>
            <span>{trendConfig.icon}</span>
            <span>{metric.change > 0 ? '+' : ''}{metric.change}%</span>
          </div>
        )}
      </div>
      
      <div className="text-2xl font-bold text-gray-900 dark:text-white mb-1">
        {typeof metric.value === 'number' ? (
          <NumberTicker 
            value={metric.value} 
            decimalPlaces={metric.format === 'currency' ? 2 : 0}
          />
        ) : (
          metric.value
        )}
      </div>
      
      <div className="text-sm text-gray-600 dark:text-gray-400">
        {metric.label}
      </div>
    </motion.div>
  );
});

SectionMetricCard.displayName = 'SectionMetricCard';

// Componente principal da seção avançada
const AdvancedSection = memo(({
  config,
  actions = [],
  metrics = [],
  children,
  className = '',
  onRefresh,
  loading = false,
  error,
  lastUpdated
}: Readonly<AdvancedSectionProps>) => {
  const [isExpanded, setIsExpanded] = useState(config.defaultExpanded ?? true);
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = useCallback(async () => {
    if (!onRefresh || isRefreshing) return;
    
    setIsRefreshing(true);
    try {
      await onRefresh();
    } finally {
      setIsRefreshing(false);
    }
  }, [onRefresh, isRefreshing]);

  const getPriorityColor = () => {
    switch (config.priority) {
      case 'high':
        return 'border-red-200 dark:border-red-800';
      case 'medium':
        return 'border-yellow-200 dark:border-yellow-800';
      default:
        return 'border-gray-200 dark:border-gray-700';
    }
  };

  const getActionVariant = (variant: string) => {
    const variants = {
      primary: { bg: '#3B82F620', shimmer: '#3B82F6', text: 'text-blue-600' },
      secondary: { bg: '#6B728020', shimmer: '#6B7280', text: 'text-gray-600' },
      danger: { bg: '#EF444420', shimmer: '#EF4444', text: 'text-red-600' },
      success: { bg: '#10B98120', shimmer: '#10B981', text: 'text-green-600' }
    };
    return variants[variant as keyof typeof variants] || variants.secondary;
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className={`${className} ${getPriorityColor()}`}
    >
      <MagicCard
        className="overflow-hidden"
        gradientColor={config.gradientColor}
        gradientSize={200}
      >
        {/* Header da Seção */}
        <div className="p-6 border-b border-gray-200 dark:border-gray-700">
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-4">
              <motion.div
                className="text-3xl"
                whileHover={{ scale: 1.1, rotate: 5 }}
                transition={{ duration: 0.2 }}
              >
                {config.icon}
              </motion.div>
              
              <div className="flex-1">
                <div className="flex items-center space-x-3">
                  <TextAnimate
                    animation="blurInUp"
                    className="text-xl font-semibold text-gray-900 dark:text-white"
                  >
                    {config.title}
                  </TextAnimate>
                  
                  {/* Badge de prioridade */}
                  <span className={`px-2 py-1 text-xs rounded-full ${
                    config.priority === 'high' ? 'bg-red-100 text-red-700' :
                    config.priority === 'medium' ? 'bg-yellow-100 text-yellow-700' :
                    'bg-gray-100 text-gray-700'
                  }`}>
                    {config.priority}
                  </span>
                  
                  {config.collapsible && (
                    <motion.button
                      onClick={() => setIsExpanded(!isExpanded)}
                      className="text-gray-500 hover:text-gray-700 dark:hover:text-gray-300"
                      whileHover={{ scale: 1.1 }}
                      whileTap={{ scale: 0.9 }}
                      aria-label={isExpanded ? 'Recolher seção' : 'Expandir seção'}
                    >
                      <motion.div
                        animate={{ rotate: isExpanded ? 180 : 0 }}
                        transition={{ duration: 0.2 }}
                      >
                        ⌄
                      </motion.div>
                    </motion.button>
                  )}
                </div>
                
                <motion.p
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.2 }}
                  className="text-sm text-gray-600 dark:text-gray-400 mt-1"
                >
                  {config.description}
                </motion.p>
                
                {/* Última atualização */}
                {lastUpdated && (
                  <motion.p
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    transition={{ delay: 0.3 }}
                    className="text-xs text-gray-500 dark:text-gray-500 mt-1"
                  >
                    Atualizado: {lastUpdated.toLocaleString('pt-BR')}
                  </motion.p>
                )}
              </div>
            </div>

            {/* Ações da seção */}
            <div className="flex items-center space-x-2">
              {/* Botão de refresh */}
              {onRefresh && (
                <motion.button
                  onClick={handleRefresh}
                  disabled={isRefreshing}
                  className="p-2 text-gray-500 hover:text-gray-700 dark:hover:text-gray-300 disabled:opacity-50"
                  whileHover={{ scale: 1.1 }}
                  whileTap={{ scale: 0.9 }}
                  aria-label="Atualizar seção"
                >
                  <motion.div
                    animate={{ rotate: isRefreshing ? 360 : 0 }}
                    transition={{ duration: 1, repeat: isRefreshing ? Infinity : 0 }}
                  >
                    🔄
                  </motion.div>
                </motion.button>
              )}

              {/* Ações customizadas */}
              {actions.map((action, index) => {
                const variant = getActionVariant(action.variant);
                
                return (
                  <div key={action.id} className="relative">
                    <ShimmerButton
                      onClick={action.onClick}
                      disabled={action.disabled || action.loading}
                      className={`text-xs px-3 py-2 ${variant.text}`}
                      shimmerColor={variant.shimmer}
                      background={variant.bg}
                    >
                      <div className="flex items-center space-x-1">
                        <span>{action.icon}</span>
                        <span>{action.label}</span>
                        {action.loading && (
                          <motion.div
                            animate={{ rotate: 360 }}
                            transition={{ duration: 1, repeat: Infinity }}
                            className="w-3 h-3"
                          >
                            ⟳
                          </motion.div>
                        )}
                      </div>
                    </ShimmerButton>
                    
                    {/* Badge da ação */}
                    {action.badge && (
                      <motion.div
                        className="absolute -top-2 -right-2 bg-red-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center"
                        animate={{ scale: [1, 1.1, 1] }}
                        transition={{ duration: 2, repeat: Infinity }}
                      >
                        {action.badge}
                      </motion.div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
        </div>

        {/* Métricas da seção */}
        {metrics.length > 0 && isExpanded && (
          <motion.div
            initial={{ opacity: 0, height: 0 }}
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            className="p-6 border-b border-gray-200 dark:border-gray-700"
          >
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
              {metrics.map((metric, index) => (
                <SectionMetricCard
                  key={metric.label}
                  metric={metric}
                  delay={index * 0.1}
                />
              ))}
            </div>
          </motion.div>
        )}

        {/* Conteúdo da seção */}
        <AnimatePresence>
          {isExpanded && (
            <motion.div
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: 'auto', opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden"
            >
              <div className="p-6">
                {loading ? (
                  <div className="flex items-center justify-center py-8">
                    <motion.div
                      animate={{ rotate: 360 }}
                      transition={{ duration: 1, repeat: Infinity }}
                      className="text-2xl"
                    >
                      ⟳
                    </motion.div>
                    <span className="ml-2 text-gray-600 dark:text-gray-400">
                      Carregando...
                    </span>
                  </div>
                ) : error ? (
                  <div className="text-center py-8">
                    <div className="text-4xl mb-2">⚠️</div>
                    <div className="text-red-600 dark:text-red-400 font-medium">
                      {error}
                    </div>
                  </div>
                ) : (
                  children
                )}
              </div>
            </motion.div>
          )}
        </AnimatePresence>
      </MagicCard>
    </motion.div>
  );
});

AdvancedSection.displayName = 'AdvancedSection';

export { AdvancedSection, type SectionConfig, type SectionAction, type SectionMetric };
