import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { DashboardMagicLayout } from '../DashboardMagicLayout';
import { InformacoesEmpresaMagic } from '../InformacoesEmpresaMagic';
import { MetricasNegocioMagic } from '../MetricasNegocioMagic';
import { StatusPlanoSaasMagic } from '../StatusPlanoSaasMagic';
import { AlertasDashboardMagic } from '../AlertasDashboardMagic';
import { AcoesRapidasMagic } from '../AcoesRapidasMagic';

// Mock dos hooks personalizados
jest.mock('@/hooks/useEmpresaProprietario', () => ({
  useEmpresaProprietario: () => ({
    empresa: {
      nome_empresa: 'Empresa Teste',
      segmento: 'Tecnologia',
      endereco: 'Rua Teste, 123',
      numero: '123',
      bairro: 'Centro',
      cidade: 'São Paulo'
    },
    planoSaas: { nome_plano: 'Premium' },
    loading: false,
    error: null,
    temEmpresa: true,
    empresaAtiva: true,
    podeReceberPagamentos: true,
    precisaConfiguracaoInicial: false,
    obterProximosPassos: () => []
  })
}));

jest.mock('@/hooks/useMetricasNegocio', () => ({
  useMetricasNegocio: () => ({
    loading: false,
    error: null,
    totalAgendamentos: 150,
    agendamentosHoje: 8,
    receitaMensal: 15000.50,
    clientesAtivos: 45,
    taxaCancelamento: 2.5,
    avaliacaoMedia: 4.8
  })
}));

jest.mock('@/hooks/usePlanoSaas', () => ({
  usePlanoSaas: () => ({
    planoAtual: { nome: 'Premium', valor: 99.90, descricao: 'Plano completo' },
    limitesPlano: { agendamentos: 500, usuarios: 10, armazenamento: 5000 },
    usoAtual: { agendamentos: 150, usuarios: 3, armazenamento: 1200 },
    diasRestantes: 25,
    proximoVencimento: '2024-02-15',
    statusPagamento: 'ativo',
    loading: false,
    error: null,
    podeUpgrade: true,
    precisaRenovar: false,
    planoExpirado: false
  })
}));

jest.mock('@/hooks/useAlertas', () => ({
  useAlertas: () => ({
    alertas: [
      {
        id: '1',
        tipo: 'info',
        titulo: 'Bem-vindo!',
        mensagem: 'Dashboard atualizada com sucesso',
        timestamp: new Date(),
        acao: { texto: 'Ver mais', href: '/detalhes' }
      }
    ],
    loading: false,
    error: null,
    alertasNaoLidos: 1,
    marcarComoLido: jest.fn(),
    dismissAlert: jest.fn()
  })
}));

jest.mock('@/hooks/useAcoesRapidas', () => ({
  useAcoesRapidas: () => ({
    loading: false,
    error: null,
    agendamentosHoje: 8,
    notificacoesPendentes: 3,
    tarefasPendentes: 5,
    executarAcao: jest.fn()
  })
}));

// Mock do framer-motion para evitar problemas de animação nos testes
jest.mock('framer-motion', () => ({
  motion: {
    div: ({ children, ...props }: any) => <div {...props}>{children}</div>,
    span: ({ children, ...props }: any) => <span {...props}>{children}</span>,
    button: ({ children, ...props }: any) => <button {...props}>{children}</button>,
    p: ({ children, ...props }: any) => <p {...props}>{children}</p>,
  },
  AnimatePresence: ({ children }: any) => <>{children}</>,
  useMotionValue: () => ({ set: jest.fn(), on: jest.fn() }),
  useSpring: () => ({ set: jest.fn(), on: jest.fn() }),
  useInView: () => true,
  useScroll: () => ({ scrollYProgress: { set: jest.fn(), on: jest.fn() } }),
  useMotionTemplate: () => 'test-template'
}));

describe('Dashboard Magic Components', () => {
  beforeEach(() => {
    // Mock do window.matchMedia para testes de responsividade
    Object.defineProperty(window, 'matchMedia', {
      writable: true,
      value: jest.fn().mockImplementation(query => ({
        matches: false,
        media: query,
        onchange: null,
        addListener: jest.fn(),
        removeListener: jest.fn(),
        addEventListener: jest.fn(),
        removeEventListener: jest.fn(),
        dispatchEvent: jest.fn(),
      })),
    });

    // Mock do window.scrollTo
    window.scrollTo = jest.fn();
  });

  describe('DashboardMagicLayout', () => {
    it('deve renderizar o layout principal', () => {
      render(<DashboardMagicLayout />);
      
      expect(screen.getByText('Dashboard do Proprietário')).toBeInTheDocument();
      expect(screen.getByText('Gerencie seu negócio com eficiência e estilo')).toBeInTheDocument();
    });

    it('deve ser responsivo', () => {
      const { rerender } = render(<DashboardMagicLayout />);
      
      // Simular tela mobile
      Object.defineProperty(window, 'innerWidth', {
        writable: true,
        configurable: true,
        value: 500,
      });
      
      rerender(<DashboardMagicLayout />);
      
      // Verificar se o layout se adapta
      expect(screen.getByText('Dashboard do Proprietário')).toBeInTheDocument();
    });
  });

  describe('InformacoesEmpresaMagic', () => {
    it('deve exibir informações da empresa', () => {
      render(<InformacoesEmpresaMagic />);
      
      expect(screen.getByText('Empresa Teste')).toBeInTheDocument();
      expect(screen.getByText('Tecnologia')).toBeInTheDocument();
      expect(screen.getByText(/Rua Teste, 123/)).toBeInTheDocument();
    });

    it('deve mostrar status ativo', () => {
      render(<InformacoesEmpresaMagic />);
      
      expect(screen.getByText('✅ Ativa')).toBeInTheDocument();
      expect(screen.getByText('💳 Pagamentos Ativos')).toBeInTheDocument();
    });
  });

  describe('MetricasNegocioMagic', () => {
    it('deve exibir métricas de negócio', () => {
      render(<MetricasNegocioMagic />);
      
      expect(screen.getByText('Métricas de Negócio')).toBeInTheDocument();
      expect(screen.getByText('Agendamentos Hoje')).toBeInTheDocument();
      expect(screen.getByText('Total de Agendamentos')).toBeInTheDocument();
      expect(screen.getByText('Receita Mensal')).toBeInTheDocument();
    });
  });

  describe('StatusPlanoSaasMagic', () => {
    it('deve exibir status do plano', () => {
      render(<StatusPlanoSaasMagic />);
      
      expect(screen.getByText('Status do Plano SaaS')).toBeInTheDocument();
      expect(screen.getByText('✅ Plano Ativo')).toBeInTheDocument();
      expect(screen.getByText('Premium')).toBeInTheDocument();
    });

    it('deve mostrar barras de progresso', () => {
      render(<StatusPlanoSaasMagic />);
      
      expect(screen.getByText('Agendamentos')).toBeInTheDocument();
      expect(screen.getByText('Usuários')).toBeInTheDocument();
      expect(screen.getByText('Armazenamento (MB)')).toBeInTheDocument();
    });
  });

  describe('AlertasDashboardMagic', () => {
    it('deve exibir alertas', () => {
      render(<AlertasDashboardMagic />);
      
      expect(screen.getByText('Alertas e Notificações')).toBeInTheDocument();
      expect(screen.getByText('Bem-vindo!')).toBeInTheDocument();
      expect(screen.getByText('Dashboard atualizada com sucesso')).toBeInTheDocument();
    });

    it('deve permitir filtrar alertas', () => {
      render(<AlertasDashboardMagic />);
      
      const filtroTodos = screen.getByText('Todos');
      const filtroInfo = screen.getByText('Info');
      
      expect(filtroTodos).toBeInTheDocument();
      expect(filtroInfo).toBeInTheDocument();
      
      fireEvent.click(filtroInfo);
      // Verificar se o filtro foi aplicado
      expect(screen.getByText('Bem-vindo!')).toBeInTheDocument();
    });
  });

  describe('AcoesRapidasMagic', () => {
    it('deve exibir ações rápidas', () => {
      render(<AcoesRapidasMagic />);
      
      expect(screen.getByText('Ações Rápidas')).toBeInTheDocument();
      expect(screen.getByText('Novo Agendamento')).toBeInTheDocument();
      expect(screen.getByText('Gerenciar Clientes')).toBeInTheDocument();
      expect(screen.getByText('Relatórios')).toBeInTheDocument();
    });

    it('deve mostrar estatísticas rápidas', () => {
      render(<AcoesRapidasMagic />);
      
      expect(screen.getByText('Agendamentos Hoje')).toBeInTheDocument();
      expect(screen.getByText('Notificações')).toBeInTheDocument();
      expect(screen.getByText('Tarefas Pendentes')).toBeInTheDocument();
    });
  });

  describe('Acessibilidade', () => {
    it('deve ter elementos focáveis', () => {
      render(<DashboardMagicLayout />);
      
      // Verificar se botões são focáveis
      const buttons = screen.getAllByRole('button');
      buttons.forEach(button => {
        expect(button).toBeVisible();
      });
    });

    it('deve ter textos alternativos apropriados', () => {
      render(<DashboardMagicLayout />);
      
      // Verificar se há textos descritivos
      expect(screen.getByText('Dashboard do Proprietário')).toBeInTheDocument();
      expect(screen.getByText('Gerencie seu negócio com eficiência e estilo')).toBeInTheDocument();
    });
  });

  describe('Performance', () => {
    it('deve carregar rapidamente', async () => {
      const startTime = performance.now();
      render(<DashboardMagicLayout />);
      const endTime = performance.now();
      
      // Verificar se carrega em menos de 100ms (tempo arbitrário para teste)
      expect(endTime - startTime).toBeLessThan(100);
    });

    it('deve lidar com estados de loading', () => {
      // Mock do hook para retornar loading
      jest.doMock('@/hooks/useEmpresaProprietario', () => ({
        useEmpresaProprietario: () => ({
          loading: true,
          error: null,
          empresa: null,
          temEmpresa: false
        })
      }));

      render(<InformacoesEmpresaMagic />);
      
      // Verificar se mostra estado de loading
      expect(screen.getByRole('generic')).toHaveClass('animate-pulse');
    });
  });
});
