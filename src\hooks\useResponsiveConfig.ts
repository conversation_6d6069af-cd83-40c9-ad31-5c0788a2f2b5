'use client';

import { useState, useEffect } from 'react';

interface ResponsiveConfig {
  isMobile: boolean;
  isTablet: boolean;
  isDesktop: boolean;
  prefersReducedMotion: boolean;
  screenWidth: number;
  screenHeight: number;
}

export function useResponsiveConfig(): ResponsiveConfig {
  const [config, setConfig] = useState<ResponsiveConfig>({
    isMobile: false,
    isTablet: false,
    isDesktop: true,
    prefersReducedMotion: false,
    screenWidth: 1920,
    screenHeight: 1080
  });

  useEffect(() => {
    const updateConfig = () => {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      const isMobile = width < 768;
      const isTablet = width >= 768 && width < 1024;
      const isDesktop = width >= 1024;
      
      // Detectar preferência de movimento reduzido
      const prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches;

      setConfig({
        isMobile,
        isTablet,
        isDesktop,
        prefersReducedMotion,
        screenWidth: width,
        screenHeight: height
      });
    };

    // Atualizar na montagem
    updateConfig();

    // Listener para mudanças de tamanho
    window.addEventListener('resize', updateConfig);

    // Listener para mudanças de preferência de movimento
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    mediaQuery.addEventListener('change', updateConfig);

    return () => {
      window.removeEventListener('resize', updateConfig);
      mediaQuery.removeEventListener('change', updateConfig);
    };
  }, []);

  return config;
}
