/**
 * Script para testar acesso à dashboard após login
 */

const { createClient } = require('@supabase/supabase-js');

// Configuração do Supabase
const supabaseUrl = 'https://tlbpsdgoklkekoxzmzlo.supabase.co';
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InRsYnBzZGdva2xrZWtveHptemxvIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2OTI4ODYsImV4cCI6MjA2NDI2ODg4Nn0._0Jj8aLx_WTyMkFoiFviThur0EW5jC3hYVOoVrrntWA';

const supabase = createClient(supabaseUrl, supabaseKey);

// Credenciais da <PERSON>
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'senha123';

async function testDashboardAccess() {
  console.log('🧪 Testando acesso à dashboard após correções...');
  console.log('=' .repeat(60));

  try {
    // 1. Login
    console.log('\n1️⃣ Fazendo login...');
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: TEST_EMAIL,
      password: TEST_PASSWORD
    });

    if (loginError) {
      console.log('❌ Erro no login:', loginError.message);
      return;
    }

    console.log('✅ Login bem-sucedido!');
    console.log(`   User ID: ${loginData.user.id}`);
    console.log(`   Role: ${loginData.user.user_metadata?.role}`);

    // 2. Testar API da dashboard
    console.log('\n2️⃣ Testando API da dashboard...');
    
    // Simular requisição para a API da dashboard
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      console.log('❌ Sessão não encontrada');
      return;
    }

    // Simular chamada HTTP para a API
    const fetch = (await import('node-fetch')).default;
    
    const response = await fetch('http://localhost:3000/api/proprietario/dashboard/empresa', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
        'Cookie': `sb-tlbpsdgoklkekoxzmzlo-auth-token=${session.access_token}`
      }
    });

    const result = await response.json();

    if (!response.ok) {
      console.log('❌ Erro na API da dashboard:', result.error);
      console.log('   Status:', response.status);
      return;
    }

    console.log('✅ API da dashboard funcionando!');
    console.log('   Dados recebidos:', Object.keys(result.data || {}));

    // 3. Logout
    console.log('\n3️⃣ Fazendo logout...');
    await supabase.auth.signOut();
    console.log('✅ Logout realizado!');

    // Resumo
    console.log('\n' + '=' .repeat(60));
    console.log('📊 RESULTADO DO TESTE');
    console.log('=' .repeat(60));
    console.log('✅ Login: OK');
    console.log('✅ API Dashboard: OK');
    console.log('✅ Logout: OK');
    console.log('\n🎉 Problema de autenticação RESOLVIDO!');

  } catch (error) {
    console.error('\n💥 Erro durante o teste:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n⚠️ Servidor não está rodando. Execute: npm run dev');
    }
  }
}

// Executar teste
if (require.main === module) {
  testDashboardAccess().then(() => {
    console.log('\n🏁 Teste finalizado.');
    process.exit(0);
  }).catch(error => {
    console.error('\n💥 Erro fatal:', error);
    process.exit(1);
  });
}

module.exports = { testDashboardAccess };
