'use client';

import React, { memo } from 'react';
import { MagicCard } from '@/components/ui/magic-card';
import { NumberTicker } from '@/components/ui/number-ticker';
import { TextAnimate } from '@/components/ui/text-animate';
import { motion } from 'framer-motion';
import { useMetricasNegocio } from '@/hooks/useMetricasNegocio';
import { LoadingSkeleton, ErrorState } from '@/components/ui/LoadingStates';
import { ShimmerButton } from '@/components/ui/shimmer-button';

interface MetricaCardProps {
  titulo: string;
  valor: number;
  prefixo?: string;
  sufixo?: string;
  icone: string;
  cor: string;
  gradientColor: string;
  tendencia?: 'up' | 'down' | 'stable';
  percentualMudanca?: number;
  delay?: number;
  decimalPlaces?: number;
}

const MetricaCard = memo(({
  titulo,
  valor,
  prefixo = '',
  sufixo = '',
  icone,
  cor,
  gradientColor,
  tendencia,
  percentualMudanca,
  delay = 0,
  decimalPlaces = 0
}: Readonly<MetricaCardProps>) => {
  const getTendenciaIcon = () => {
    switch (tendencia) {
      case 'up': return '📈';
      case 'down': return '📉';
      case 'stable': return '➡️';
      default: return '';
    }
  };

  const getTendenciaColor = () => {
    switch (tendencia) {
      case 'up': return 'text-green-400';
      case 'down': return 'text-red-400';
      case 'stable': return 'text-yellow-400';
      default: return 'text-gray-400';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay, duration: 0.5 }}
    >
      <MagicCard 
        className="h-full p-6 hover:scale-105 transition-transform duration-300"
        gradientColor={gradientColor}
        gradientSize={250}
      >
        <div className="relative z-10">
          {/* Header com ícone */}
          <div className="flex items-center justify-between mb-4">
            <motion.div 
              className="text-3xl"
              animate={{ rotate: [0, 10, -10, 0] }}
              transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
            >
              {icone}
            </motion.div>
            
            {tendencia && percentualMudanca !== undefined && (
              <motion.div 
                className={`flex items-center space-x-1 text-xs ${getTendenciaColor()}`}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: delay + 0.3 }}
              >
                <span>{getTendenciaIcon()}</span>
                <span>{Math.abs(percentualMudanca).toFixed(1)}%</span>
              </motion.div>
            )}
          </div>

          {/* Título */}
          <TextAnimate
            animation="blurInUp"
            className="text-sm font-medium text-white/80 mb-2"
            delay={delay + 0.1}
          >
            {titulo}
          </TextAnimate>

          {/* Valor principal */}
          <div className="flex items-baseline space-x-1">
            {prefixo && (
              <motion.span 
                className="text-lg font-semibold text-white/90"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: delay + 0.2 }}
              >
                {prefixo}
              </motion.span>
            )}
            
            <NumberTicker
              value={valor}
              className="text-3xl font-bold text-white"
              delay={delay + 0.2}
              decimalPlaces={decimalPlaces}
            />
            
            {sufixo && (
              <motion.span 
                className="text-lg font-semibold text-white/90"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: delay + 0.4 }}
              >
                {sufixo}
              </motion.span>
            )}
          </div>

          {/* Barra de progresso animada (opcional) */}
          <motion.div 
            className="mt-4 h-1 bg-white/20 rounded-full overflow-hidden"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: delay + 0.5 }}
          >
            <motion.div 
              className={`h-full ${cor} rounded-full`}
              initial={{ width: 0 }}
              animate={{ width: `${Math.min((valor / 1000) * 100, 100)}%` }}
              transition={{ delay: delay + 0.6, duration: 1, ease: "easeOut" }}
            />
          </motion.div>
        </div>

        {/* Efeito de brilho no hover */}
        <motion.div 
          className="absolute inset-0 bg-gradient-to-r from-transparent via-white/10 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-500"
          style={{
            background: `linear-gradient(90deg, transparent, ${gradientColor}20, transparent)`,
          }}
          animate={{
            x: ['-100%', '100%'],
          }}
          transition={{
            duration: 2,
            repeat: Infinity,
            repeatDelay: 3,
          }}
        />
      </MagicCard>
    </motion.div>
  );
});

interface MetricasNegocioMagicProps {
  className?: string;
}

const MetricasNegocioMagicComponent = ({ className = '' }: Readonly<MetricasNegocioMagicProps>) => {
  const {
    loading,
    error,
    totalAgendamentos,
    agendamentosHoje,
    receitaMensal,
    clientesAtivos,
    taxaCancelamento,
    avaliacaoMedia
  } = useMetricasNegocio();

  // Debug logs
  console.log('📊 MetricasNegocioMagic: Estado atual:', {
    loading,
    error,
    totalAgendamentos,
    agendamentosHoje,
    receitaMensal,
    clientesAtivos
  });

  // Gerar IDs únicos para as partículas
  const particleIds = React.useMemo(() =>
    Array.from({ length: 8 }, (_, i) => `metrics-particle-${Date.now()}-${i}`)
  , []);

  if (loading) {
    return (
      <LoadingSkeleton
        variant="metrics"
        count={6}
        className={`grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 ${className}`}
      />
    );
  }

  if (error) {
    return (
      <ErrorState
        title="Erro ao carregar métricas"
        message="Não foi possível carregar as métricas de negócio. Tente novamente."
        className={className}
      />
    );
  }

  // Se não há dados mas não está carregando, mostrar estado vazio
  if (!loading && totalAgendamentos === 0 && receitaMensal === 0 && clientesAtivos === 0) {
    return (
      <MagicCard className={`p-8 text-center ${className}`} gradientColor="#6B7280">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          <div className="text-6xl">📊</div>
          <TextAnimate
            animation="blurInUp"
            className="text-xl font-semibold text-gray-700 dark:text-gray-300"
          >
            Nenhuma métrica disponível
          </TextAnimate>
          <p className="text-gray-500 dark:text-gray-400">
            Comece criando agendamentos para ver suas métricas de negócio aqui.
          </p>
          <div className="mt-4">
            <ShimmerButton
              className="bg-blue-600 hover:bg-blue-700"
              shimmerColor="#60A5FA"
            >
              Criar Primeiro Agendamento
            </ShimmerButton>
          </div>
        </motion.div>
      </MagicCard>
    );
  }

  const metricas_cards = [
    {
      titulo: 'Agendamentos Hoje',
      valor: agendamentosHoje,
      icone: '📅',
      cor: 'bg-blue-500',
      gradientColor: '#3B82F6',
      tendencia: 'up' as const,
      percentualMudanca: 12.5,
      delay: 0
    },
    {
      titulo: 'Total de Agendamentos',
      valor: totalAgendamentos,
      icone: '📋',
      cor: 'bg-green-500',
      gradientColor: '#10B981',
      tendencia: 'up' as const,
      percentualMudanca: 8.3,
      delay: 0.1
    },
    {
      titulo: 'Receita Mensal',
      valor: receitaMensal,
      prefixo: 'R$',
      icone: '💰',
      cor: 'bg-yellow-500',
      gradientColor: '#F59E0B',
      tendencia: 'up' as const,
      percentualMudanca: 15.7,
      delay: 0.2,
      decimalPlaces: 2
    },
    {
      titulo: 'Clientes Ativos',
      valor: clientesAtivos,
      icone: '👥',
      cor: 'bg-purple-500',
      gradientColor: '#8B5CF6',
      tendencia: 'stable' as const,
      percentualMudanca: 2.1,
      delay: 0.3
    },
    {
      titulo: 'Taxa de Cancelamento',
      valor: taxaCancelamento,
      sufixo: '%',
      icone: '❌',
      cor: 'bg-red-500',
      gradientColor: '#EF4444',
      tendencia: 'down' as const,
      percentualMudanca: -3.2,
      delay: 0.4,
      decimalPlaces: 1
    },
    {
      titulo: 'Avaliação Média',
      valor: avaliacaoMedia,
      sufixo: '/5',
      icone: '⭐',
      cor: 'bg-orange-500',
      gradientColor: '#F97316',
      tendencia: 'up' as const,
      percentualMudanca: 4.8,
      delay: 0.5,
      decimalPlaces: 1
    }
  ];

  return (
    <div className={className}>
      {/* Título da seção */}
      <div className="mb-6">
        <TextAnimate
          animation="slideRight"
          className="text-2xl font-bold text-gray-900 dark:text-white"
          by="word"
        >
          Métricas de Negócio
        </TextAnimate>
        <motion.p 
          className="text-gray-600 dark:text-gray-400 mt-2"
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.2 }}
        >
          Acompanhe o desempenho do seu negócio em tempo real
        </motion.p>
      </div>

      {/* Grid de métricas */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {metricas_cards.map((metrica, index) => (
          <MetricaCard
            key={metrica.titulo}
            {...metrica}
          />
        ))}
      </div>

      {/* Efeito de partículas flutuantes */}
      <div className="absolute inset-0 pointer-events-none overflow-hidden">
        {particleIds.map((id, i) => (
          <motion.div
            key={id}
            className="absolute w-1 h-1 bg-blue-400 rounded-full opacity-30"
            style={{
              left: `${Math.random() * 100}%`,
              top: `${Math.random() * 100}%`,
            }}
            animate={{
              y: [0, -20, 0],
              x: [0, Math.random() * 10 - 5, 0],
              opacity: [0.3, 0.8, 0.3],
            }}
            transition={{
              duration: 4 + Math.random() * 2,
              repeat: Infinity,
              delay: Math.random() * 3,
            }}
          />
        ))}
      </div>
    </div>
  );
};

// Memoizar componente para evitar re-renders desnecessários
export const MetricasNegocioMagic = memo(MetricasNegocioMagicComponent, (prevProps, nextProps) => {
  return prevProps.className === nextProps.className;
});
