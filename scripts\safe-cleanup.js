#!/usr/bin/env node

/**
 * Script de limpeza segura
 * Remove apenas arquivos claramente desnecessários
 */

const fs = require('fs');
const path = require('path');

// Arquivos que podem ser removidos com segurança
const SAFE_TO_REMOVE = [
  // Utilitários não utilizados
  'src/utils/slug.ts', // Função simples que pode ser recriada se necessário
  'src/utils/serviceWorker.ts', // Service worker já está em public/sw.js
  
  // Componentes duplicados ou obsoletos
  'src/components/ui/LoadingStates.tsx', // Já temos componentes de loading em outros lugares
  
  // Arquivos de desenvolvimento/teste não utilizados
  'src/utils/monitoring/performanceMiddleware.ts', // Middleware não implementado
];

// Arquivos que devem ser mantidos (mesmo se não referenciados diretamente)
const KEEP_FILES = [
  // Componentes que podem ser usados no futuro próximo
  'src/components/auth/FormularioCadastro.tsx',
  'src/components/colaboradores/FormularioConviteColaborador.tsx',
  'src/components/branding/BrandingCustomizer.tsx',
  'src/components/branding/CompanyBrandingWrapper.tsx',
  'src/components/forms/AccessibleForm.tsx',
  'src/components/ui/AccessibleTable.tsx',
  'src/components/ui/ContextualTooltip.tsx',
  'src/components/horarios/BloqueioHorarios.tsx',
  'src/components/onboarding/OnboardingChecklist.tsx',
  
  // Hooks otimizados recém-criados
  'src/hooks/useColaboradoresComCache.ts',
  'src/hooks/useServicosComCache.ts',
  
  // Componentes novos otimizados
  'src/components/servicos/ListaServicos.tsx',
  'src/components/agendamento/SeletorServicos.tsx',
  
  // Utilitários importantes
  'src/utils/api/apiHelpers.ts',
  'src/utils/marketing.ts',
  'src/utils/stripe/agendamentos.ts'
];

function removeFile(filePath) {
  try {
    const fullPath = path.join(__dirname, '..', filePath);
    if (fs.existsSync(fullPath)) {
      fs.unlinkSync(fullPath);
      console.log(`✅ Removido: ${filePath}`);
      return true;
    } else {
      console.log(`⚠️  Arquivo não encontrado: ${filePath}`);
      return false;
    }
  } catch (error) {
    console.error(`❌ Erro ao remover ${filePath}:`, error.message);
    return false;
  }
}

function createBackup() {
  const backupDir = path.join(__dirname, '..', 'backup-cleanup');
  if (!fs.existsSync(backupDir)) {
    fs.mkdirSync(backupDir, { recursive: true });
  }
  
  console.log(`📦 Criando backup em: ${backupDir}`);
  
  for (const file of SAFE_TO_REMOVE) {
    const sourcePath = path.join(__dirname, '..', file);
    if (fs.existsSync(sourcePath)) {
      const backupPath = path.join(backupDir, path.basename(file));
      fs.copyFileSync(sourcePath, backupPath);
      console.log(`   • Backup: ${file} → ${path.basename(file)}`);
    }
  }
}

function cleanupDependencies() {
  console.log('\n🔍 Verificando dependências não utilizadas...');
  
  const packageJsonPath = path.join(__dirname, '..', 'package.json');
  const packageJson = JSON.parse(fs.readFileSync(packageJsonPath, 'utf8'));
  
  // Dependências que podem estar não utilizadas (verificar manualmente)
  const potentiallyUnused = [
    'isomorphic-dompurify', // Pode não estar sendo usado
    'class-variance-authority' // Verificar se está sendo usado nos componentes UI
  ];
  
  console.log('📋 Dependências que podem estar não utilizadas:');
  for (const dep of potentiallyUnused) {
    if (packageJson.dependencies && packageJson.dependencies[dep]) {
      console.log(`   • ${dep}: ${packageJson.dependencies[dep]}`);
    }
    if (packageJson.devDependencies && packageJson.devDependencies[dep]) {
      console.log(`   • ${dep}: ${packageJson.devDependencies[dep]} (dev)`);
    }
  }
  
  console.log('\n⚠️  Verifique manualmente se essas dependências são realmente necessárias');
}

function optimizeImports() {
  console.log('\n🔧 Verificando imports que podem ser otimizados...');
  
  // Verificar se há imports de bibliotecas inteiras quando apenas funções específicas são usadas
  const optimizationSuggestions = [
    {
      pattern: "import * as React from 'react'",
      suggestion: "import React, { useState, useEffect, ... } from 'react'",
      reason: "Importar apenas as funções necessárias do React"
    },
    {
      pattern: "import { format } from 'date-fns'",
      suggestion: "import format from 'date-fns/format'",
      reason: "Importar funções específicas do date-fns para reduzir bundle"
    }
  ];
  
  console.log('💡 Sugestões de otimização de imports:');
  for (const suggestion of optimizationSuggestions) {
    console.log(`   • ${suggestion.pattern}`);
    console.log(`     → ${suggestion.suggestion}`);
    console.log(`     Motivo: ${suggestion.reason}\n`);
  }
}

function generateCleanupReport() {
  console.log('\n📊 RELATÓRIO DE LIMPEZA');
  console.log('=======================\n');
  
  console.log('✅ Arquivos removidos com segurança:');
  console.log(`   • Total: ${SAFE_TO_REMOVE.length} arquivos`);
  console.log('   • Backup criado em: backup-cleanup/\n');
  
  console.log('🛡️  Arquivos mantidos (potencialmente úteis):');
  console.log(`   • Total: ${KEEP_FILES.length} arquivos`);
  console.log('   • Motivo: Podem ser necessários para funcionalidades futuras\n');
  
  console.log('📈 Benefícios esperados:');
  console.log('   • Redução do tamanho do projeto');
  console.log('   • Menos arquivos para manter');
  console.log('   • Build mais rápido');
  console.log('   • Menos confusão no desenvolvimento\n');
  
  console.log('🔄 Próximos passos recomendados:');
  console.log('   1. Executar testes para garantir que nada quebrou');
  console.log('   2. Verificar se o build funciona corretamente');
  console.log('   3. Revisar dependências sugeridas para remoção');
  console.log('   4. Considerar implementar lazy loading para componentes grandes\n');
}

function main() {
  console.log('🧹 Iniciando limpeza segura do projeto...\n');
  
  // Criar backup antes de remover
  createBackup();
  
  console.log('\n🗑️  Removendo arquivos desnecessários...');
  let removedCount = 0;
  
  for (const file of SAFE_TO_REMOVE) {
    if (removeFile(file)) {
      removedCount++;
    }
  }
  
  console.log(`\n✨ Removidos ${removedCount} de ${SAFE_TO_REMOVE.length} arquivos`);
  
  // Verificar dependências
  cleanupDependencies();
  
  // Sugestões de otimização
  optimizeImports();
  
  // Relatório final
  generateCleanupReport();
  
  console.log('🎉 Limpeza concluída com sucesso!\n');
}

if (require.main === module) {
  main();
}

module.exports = { removeFile, createBackup };
