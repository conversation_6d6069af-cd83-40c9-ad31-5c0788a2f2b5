const { createClient } = require('@supabase/supabase-js');

const supabase = createClient(
  'https://tlbpsdgoklkekoxzmzlo.supabase.co',
  'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.r-2OdjA_nPIqR7HjFg9L-R0jTrZX7QYWs5FO-W_gVp4'
);

async function criarEmpresaMariaSantos() {
  console.log('Criando empresa para <PERSON> Santos...');
  
  try {
    // Buscar o usuário <PERSON>
    const { data: users, error: userError } = await supabase.auth.admin.listUsers();
    if (userError) {
      console.error('Erro ao buscar usuários:', userError);
      return;
    }
    
    const mariaSantos = users.users.find(u => u.email === '<EMAIL>');
    if (!mariaSantos) {
      console.error('<PERSON><PERSON><PERSON><PERSON> n<PERSON> encontrado');
      return;
    }
    
    console.log('Usu<PERSON>rio encontrado:', mariaSantos.id);
    
    // Verificar se já existe empresa
    const { data: empresaExistente } = await supabase
      .from('empresas')
      .select('empresa_id, nome_empresa')
      .eq('proprietario_user_id', mariaSantos.id)
      .single();
    
    if (empresaExistente) {
      console.log('✅ Empresa já existe:', empresaExistente.nome_empresa);
      return;
    }
    
    // Criar empresa para Maria Santos
    const { data: empresa, error: empresaError } = await supabase
      .from('empresas')
      .insert({
        proprietario_user_id: mariaSantos.id,
        nome_empresa: 'Salão Maria Santos',
        cnpj: '12.345.678/0001-90',
        telefone: '(11) 99999-9999',
        endereco: 'Rua das Flores, 123',
        numero: '123',
        bairro: 'Centro',
        cidade: 'São Paulo',
        estado: 'SP',
        cep: '01234-567',
        segmento: 'Beleza e Estética',
        descricao: 'Salão de beleza especializado em cortes, coloração e tratamentos capilares.',
        slug: 'salao-maria-santos',
        status: 'ativo',
        horario_funcionamento: {
          segunda: { abertura: '09:00', fechamento: '18:00', ativo: true },
          terca: { abertura: '09:00', fechamento: '18:00', ativo: true },
          quarta: { abertura: '09:00', fechamento: '18:00', ativo: true },
          quinta: { abertura: '09:00', fechamento: '18:00', ativo: true },
          sexta: { abertura: '09:00', fechamento: '18:00', ativo: true },
          sabado: { abertura: '09:00', fechamento: '16:00', ativo: true },
          domingo: { abertura: '10:00', fechamento: '14:00', ativo: false }
        }
      })
      .select()
      .single();
    
    if (empresaError) {
      console.error('Erro ao criar empresa:', empresaError);
      return;
    }
    
    console.log('✅ Empresa criada com sucesso!');
    console.log('Nome:', empresa.nome_empresa);
    console.log('ID:', empresa.empresa_id);
    console.log('Status:', empresa.status);
    console.log('Slug:', empresa.slug);
    
    // Criar alguns serviços básicos
    console.log('\nCriando serviços básicos...');
    
    const servicos = [
      {
        empresa_id: empresa.empresa_id,
        nome: 'Corte Feminino',
        descricao: 'Corte de cabelo feminino com lavagem e finalização',
        preco: 50.00,
        duracao_minutos: 60,
        ativo: true
      },
      {
        empresa_id: empresa.empresa_id,
        nome: 'Coloração',
        descricao: 'Coloração completa do cabelo',
        preco: 120.00,
        duracao_minutos: 120,
        ativo: true
      },
      {
        empresa_id: empresa.empresa_id,
        nome: 'Escova',
        descricao: 'Escova modeladora com finalização',
        preco: 35.00,
        duracao_minutos: 45,
        ativo: true
      }
    ];
    
    const { data: servicosCriados, error: servicosError } = await supabase
      .from('servicos')
      .insert(servicos)
      .select();
    
    if (servicosError) {
      console.error('Erro ao criar serviços:', servicosError);
    } else {
      console.log(`✅ ${servicosCriados.length} serviços criados!`);
    }
    
  } catch (error) {
    console.error('Erro geral:', error);
  }
}

// Executar
criarEmpresaMariaSantos().then(() => {
  console.log('\n🏁 Processo finalizado.');
  process.exit(0);
}).catch(error => {
  console.error('Erro fatal:', error);
  process.exit(1);
});
