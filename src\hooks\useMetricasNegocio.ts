'use client';

import { useEmpresaProprietario } from './useEmpresaProprietario';

export function useMetricasNegocio() {
  const { metricas, loading, error } = useEmpresaProprietario();

  return {
    metricas,
    loading,
    error,
    totalAgendamentos: metricas?.total_agendamentos_mes || 0,
    agendamentosHoje: metricas?.agendamentos_pendentes || 0,
    receitaMensal: metricas?.receita_bruta_mes || 0,
    clientesAtivos: metricas?.total_clientes_ativos || 0,
    taxaCancelamento: 100 - (metricas?.taxa_confirmacao_mes || 0),
    avaliacaoMedia: 4.5, // <PERSON>or padrão, pode ser expandido futuramente
  };
}
