'use client';

import React, { useState, useEffect } from 'react';
import { TextAnimate } from './text-animate';

interface SafeTextAnimateProps {
  children: React.ReactNode;
  animation?: string;
  className?: string;
  by?: 'word' | 'char';
  fallbackClassName?: string;
}

export function SafeTextAnimate({ 
  children, 
  animation = 'fadeIn',
  className = '',
  by = 'word',
  fallbackClassName
}: SafeTextAnimateProps) {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  if (!isHydrated) {
    // Durante SSR, renderizar versão estática
    const finalClassName = fallbackClassName || className;

    if (typeof children === 'string') {
      return <h1 className={finalClassName}>{children}</h1>;
    }

    return <div className={finalClassName}>{children}</div>;
  }

  // Após hidratação, renderizar com animação
  return (
    <TextAnimate 
      animation={animation}
      className={className}
      by={by}
    >
      {children}
    </TextAnimate>
  );
}
