'use client';

import React, { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import Link from 'next/link';
import { Card, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';
import { useEmpresaProprietario } from '@/hooks/useEmpresaProprietario';

interface VerificacaoEmpresaProps {
  children: React.ReactNode;
  redirecionarSeNaoTiver?: boolean;
  mostrarMensagem?: boolean;
}

export function VerificacaoEmpresa({ 
  children, 
  redirecionarSeNaoTiver = false,
  mostrarMensagem = true 
}: VerificacaoEmpresaProps) {
  const router = useRouter();
  const { 
    empresa, 
    loading, 
    error, 
    temEmpresa, 
    empresaAtiva 
  } = useEmpresaProprietario();

  // Redirecionar automaticamente se configurado
  useEffect(() => {
    if (!loading && redirecionarSeNaoTiver && !temEmpresa) {
      router.push('/onboarding?redirect=dashboard');
    }
  }, [loading, temEmpresa, redirecionarSeNaoTiver, router]);

  // Loading state
  if (loading) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[var(--primary)] mx-auto"></div>
          <div className="text-[var(--text-secondary)]">
            Verificando dados da empresa...
          </div>
        </div>
      </div>
    );
  }

  // Error state
  if (error) {
    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center p-4">
        <Card className="max-w-md w-full border-red-200 bg-red-50">
          <CardContent className="p-6 text-center">
            <div className="text-red-600 text-4xl mb-4">⚠️</div>
            <h2 className="text-xl font-bold text-red-800 mb-2">
              Erro ao verificar empresa
            </h2>
            <p className="text-red-700 text-sm mb-4">
              {error}
            </p>
            <div className="space-y-2">
              <Button 
                onClick={() => window.location.reload()} 
                className="w-full bg-red-600 hover:bg-red-700"
              >
                Tentar Novamente
              </Button>
              <Link href="/">
                <Button variant="outline" className="w-full border-red-600 text-red-600">
                  Voltar ao Início
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Sem empresa
  if (!temEmpresa) {
    if (!mostrarMensagem) {
      return null;
    }

    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center p-4">
        <Card className="max-w-lg w-full border-yellow-200 bg-yellow-50">
          <CardContent className="p-8 text-center">
            <div className="text-yellow-600 text-6xl mb-6">🏢</div>
            <h2 className="text-2xl font-bold text-yellow-800 mb-4">
              Empresa não encontrada
            </h2>
            <p className="text-yellow-700 mb-6">
              Para acessar o dashboard do proprietário, você precisa ter uma empresa 
              cadastrada e ativa no ServiceTech. Complete o processo de onboarding 
              para configurar sua empresa.
            </p>
            
            <div className="space-y-4">
              <div className="bg-white p-4 rounded-lg border border-yellow-200">
                <h3 className="font-semibold text-yellow-800 mb-2">
                  O que você precisa fazer:
                </h3>
                <ul className="text-sm text-yellow-700 space-y-1 text-left">
                  <li>• Cadastrar dados da sua empresa</li>
                  <li>• Adicionar pelo menos um serviço</li>
                  <li>• Configurar horários de funcionamento</li>
                  <li>• Definir colaboradores ativos</li>
                </ul>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                <Link href="/onboarding" className="flex-1">
                  <Button className="w-full bg-yellow-600 hover:bg-yellow-700">
                    Criar Empresa
                  </Button>
                </Link>
                <Link href="/planos" className="flex-1">
                  <Button variant="outline" className="w-full border-yellow-600 text-yellow-600">
                    Ver Planos
                  </Button>
                </Link>
              </div>

              <Link href="/">
                <Button variant="ghost" className="w-full text-yellow-600">
                  Voltar ao Início
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Empresa inativa
  if (!empresaAtiva) {
    if (!mostrarMensagem) {
      return null;
    }

    return (
      <div className="min-h-screen bg-[var(--background)] flex items-center justify-center p-4">
        <Card className="max-w-lg w-full border-red-200 bg-red-50">
          <CardContent className="p-8 text-center">
            <div className="text-red-600 text-6xl mb-6">🚫</div>
            <h2 className="text-2xl font-bold text-red-800 mb-4">
              Empresa Inativa
            </h2>
            <p className="text-red-700 mb-6">
              Sua empresa <strong>{empresa?.nome_empresa}</strong> está atualmente 
              inativa. Empresas inativas não podem receber agendamentos nem acessar 
              todas as funcionalidades do dashboard.
            </p>
            
            <div className="space-y-4">
              <div className="bg-white p-4 rounded-lg border border-red-200">
                <h3 className="font-semibold text-red-800 mb-2">
                  Possíveis motivos:
                </h3>
                <ul className="text-sm text-red-700 space-y-1 text-left">
                  <li>• Configuração incompleta</li>
                  <li>• Problemas com a assinatura SaaS</li>
                  <li>• Violação dos termos de uso</li>
                  <li>• Suspensão temporária</li>
                </ul>
              </div>

              <div className="flex flex-col sm:flex-row gap-3">
                <Link href="/proprietario/configuracoes" className="flex-1">
                  <Button className="w-full bg-red-600 hover:bg-red-700">
                    Verificar Configurações
                  </Button>
                </Link>
                <Link href="/planos" className="flex-1">
                  <Button variant="outline" className="w-full border-red-600 text-red-600">
                    Verificar Plano
                  </Button>
                </Link>
              </div>

              <Link href="/">
                <Button variant="ghost" className="w-full text-red-600">
                  Voltar ao Início
                </Button>
              </Link>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  // Empresa válida - renderizar children
  return <>{children}</>;
}
