"use client";

import React from 'react';
import { motion } from 'framer-motion';
import { cn } from '@/lib/utils';
import Particles from './particles';
import { ScrollProgress } from './scroll-progress';

// Posições fixas para evitar problemas de hidratação
const FIXED_POSITIONS = {
  circles: [
    { left: 15, top: 20 },
    { left: 75, top: 35 },
    { left: 45, top: 60 },
    { left: 85, top: 15 },
    { left: 25, top: 80 },
    { left: 65, top: 25 },
    { left: 35, top: 45 },
    { left: 90, top: 70 }
  ],
  squares: [
    { left: 10, top: 40 },
    { left: 70, top: 10 },
    { left: 40, top: 75 },
    { left: 80, top: 50 },
    { left: 20, top: 65 }
  ],
  triangles: [
    { left: 30, top: 15 },
    { left: 60, top: 40 },
    { left: 50, top: 85 },
    { left: 95, top: 30 },
    { left: 5, top: 55 },
    { left: 75, top: 75 }
  ],
  sparkles: [
    { left: 12, top: 25 },
    { left: 88, top: 45 },
    { left: 55, top: 12 },
    { left: 25, top: 70 },
    { left: 78, top: 20 },
    { left: 42, top: 85 },
    { left: 65, top: 55 },
    { left: 18, top: 90 },
    { left: 92, top: 35 },
    { left: 38, top: 5 },
    { left: 72, top: 65 },
    { left: 8, top: 45 },
    { left: 58, top: 95 },
    { left: 85, top: 8 },
    { left: 28, top: 50 }
  ]
};

interface VisualEffectsProps {
  className?: string;
  showParticles?: boolean;
  showScrollProgress?: boolean;
  showFloatingElements?: boolean;
  particleColor?: string;
  particleQuantity?: number;
}

export function VisualEffects({
  className = '',
  showParticles = true,
  showScrollProgress = true,
  showFloatingElements = true,
  particleColor = '#3B82F6',
  particleQuantity = 50
}: Readonly<VisualEffectsProps>) {
  return (
    <div className={cn('fixed inset-0 pointer-events-none overflow-hidden', className)}>
      {/* Scroll Progress Bar */}
      {showScrollProgress && <ScrollProgress />}

      {/* Background Particles */}
      {showParticles && (
        <Particles
          className="absolute inset-0"
          quantity={particleQuantity}
          color={particleColor}
          size={0.8}
          staticity={30}
          ease={80}
        />
      )}

      {/* Floating Geometric Elements */}
      {showFloatingElements && (
        <>
          {/* Círculos flutuantes */}
          {FIXED_POSITIONS.circles.map((position, i) => (
            <motion.div
              key={`circle-${position.left}-${position.top}`}
              className="absolute w-4 h-4 bg-blue-400/10 rounded-full"
              style={{
                left: `${position.left}%`,
                top: `${position.top}%`,
              }}
              animate={{
                y: [0, -30, 0],
                x: [0, (i % 2 === 0 ? 10 : -10), 0],
                opacity: [0.1, 0.3, 0.1],
                scale: [1, 1.2, 1],
              }}
              transition={{
                duration: 6 + (i % 3),
                repeat: Infinity,
                delay: i * 0.5,
                ease: "easeInOut",
              }}
            />
          ))}

          {/* Quadrados rotativos */}
          {FIXED_POSITIONS.squares.map((position, i) => (
            <motion.div
              key={`square-${position.left}-${position.top}`}
              className="absolute w-3 h-3 bg-purple-400/10 rounded-sm"
              style={{
                left: `${position.left}%`,
                top: `${position.top}%`,
              }}
              animate={{
                rotate: [0, 360],
                y: [0, -40, 0],
                opacity: [0.1, 0.4, 0.1],
              }}
              transition={{
                duration: 8 + (i % 2),
                repeat: Infinity,
                delay: i * 0.6,
                ease: "linear",
              }}
            />
          ))}

          {/* Triângulos */}
          {FIXED_POSITIONS.triangles.map((position, i) => (
            <motion.div
              key={`triangle-${position.left}-${position.top}`}
              className="absolute w-0 h-0 border-l-2 border-r-2 border-b-4 border-l-transparent border-r-transparent border-b-pink-400/10"
              style={{
                left: `${position.left}%`,
                top: `${position.top}%`,
              }}
              animate={{
                rotate: [0, 180, 360],
                y: [0, -25, 0],
                opacity: [0.1, 0.3, 0.1],
              }}
              transition={{
                duration: 7 + (i % 3),
                repeat: Infinity,
                delay: i * 0.7,
                ease: "easeInOut",
              }}
            />
          ))}
        </>
      )}

      {/* Gradiente animado de fundo */}
      <motion.div
        className="absolute inset-0 bg-gradient-to-br from-blue-50/20 via-purple-50/10 to-pink-50/20 dark:from-blue-900/5 dark:via-purple-900/5 dark:to-pink-900/5"
        animate={{
          background: [
            "linear-gradient(45deg, rgba(59, 130, 246, 0.05), rgba(147, 51, 234, 0.05), rgba(236, 72, 153, 0.05))",
            "linear-gradient(135deg, rgba(147, 51, 234, 0.05), rgba(236, 72, 153, 0.05), rgba(59, 130, 246, 0.05))",
            "linear-gradient(225deg, rgba(236, 72, 153, 0.05), rgba(59, 130, 246, 0.05), rgba(147, 51, 234, 0.05))",
            "linear-gradient(315deg, rgba(59, 130, 246, 0.05), rgba(147, 51, 234, 0.05), rgba(236, 72, 153, 0.05))"
          ]
        }}
        transition={{ 
          duration: 20, 
          repeat: Infinity, 
          ease: "linear" 
        }}
      />

      {/* Ondas de luz */}
      <motion.div
        className="absolute bottom-0 left-0 w-full h-32 bg-gradient-to-t from-blue-100/10 to-transparent dark:from-blue-900/10"
        animate={{
          opacity: [0.1, 0.3, 0.1],
          scaleY: [1, 1.2, 1],
        }}
        transition={{
          duration: 6,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      {/* Raios de luz diagonais */}
      <motion.div
        className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-yellow-200/5 to-transparent dark:from-yellow-400/5"
        animate={{
          opacity: [0.1, 0.2, 0.1],
          rotate: [0, 5, 0],
        }}
        transition={{
          duration: 8,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      {/* Efeito de aurora */}
      <motion.div
        className="absolute top-1/4 left-1/4 w-96 h-96 bg-gradient-radial from-green-200/5 via-blue-200/5 to-transparent dark:from-green-400/5 dark:via-blue-400/5 rounded-full blur-3xl"
        animate={{
          scale: [1, 1.3, 1],
          opacity: [0.1, 0.2, 0.1],
          x: [0, 50, 0],
          y: [0, -30, 0],
        }}
        transition={{
          duration: 12,
          repeat: Infinity,
          ease: "easeInOut",
        }}
      />

      {/* Pontos de luz cintilantes */}
      {FIXED_POSITIONS.sparkles.map((position, i) => (
        <motion.div
          key={`sparkle-${position.left}-${position.top}`}
          className="absolute w-1 h-1 bg-white rounded-full"
          style={{
            left: `${position.left}%`,
            top: `${position.top}%`,
          }}
          animate={{
            opacity: [0, 1, 0],
            scale: [0, 1, 0],
          }}
          transition={{
            duration: 2 + (i % 2),
            repeat: Infinity,
            delay: i * 0.3,
            ease: "easeInOut",
          }}
        />
      ))}
    </div>
  );
}
