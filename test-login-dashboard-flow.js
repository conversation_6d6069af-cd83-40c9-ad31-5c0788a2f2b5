/**
 * Script para testar o fluxo completo: Login → Dashboard
 * Reproduzir o erro "Token de autenticação não encontrado"
 */

const { createClient } = require('@supabase/supabase-js');
require('dotenv').config({ path: './.env.local' });

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseUrl || !supabaseAnonKey) {
  console.error('❌ Variáveis de ambiente do Supabase não encontradas');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseAnonKey);

async function testLoginDashboardFlow() {
  try {
    console.log('🔐 TESTE DO FLUXO: LOGIN → DASHBOARD');
    console.log('=' .repeat(60));
    console.log('');

    // 1. Fazer login como <PERSON>
    console.log('1️⃣ Fazendo login como <PERSON>...');
    
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: '<EMAIL>',
      password: 'teste123456'
    });

    if (loginError) {
      console.log('❌ Erro no login:', loginError.message);
      return;
    }

    console.log('✅ Login bem-sucedido!');
    console.log(`   User ID: ${loginData.user.id}`);
    console.log(`   Email: ${loginData.user.email}`);
    console.log('');

    // 2. Obter sessão e cookies
    console.log('2️⃣ Obtendo sessão e analisando cookies...');
    
    const { data: { session }, error: sessionError } = await supabase.auth.getSession();
    
    if (sessionError || !session) {
      console.log('❌ Erro ao obter sessão:', sessionError?.message || 'Sessão não encontrada');
      return;
    }

    console.log('✅ Sessão obtida com sucesso!');
    console.log(`   Access Token: ${session.access_token.substring(0, 50)}...`);
    console.log(`   Refresh Token: ${session.refresh_token.substring(0, 30)}...`);
    console.log('');

    // 3. Simular cookies que o navegador enviaria
    console.log('3️⃣ Simulando cookies do navegador...');
    
    const projectRef = 'tlbpsdgoklkekoxzmzlo';
    const cookies = [
      `sb-${projectRef}-auth-token=${session.access_token}`,
      `sb-${projectRef}-auth-token.0=${session.access_token}`,
      `sb-${projectRef}-auth-token.1=${session.refresh_token}`
    ].join('; ');
    
    console.log('✅ Cookies simulados:');
    console.log(`   Formato: sb-${projectRef}-auth-token=...`);
    console.log(`   Total de cookies: 3`);
    console.log('');

    // 4. Testar API do dashboard
    console.log('4️⃣ Testando API do dashboard...');
    
    const response = await fetch('http://localhost:3001/api/proprietario/dashboard/empresa', {
      method: 'GET',
      headers: {
        'Content-Type': 'application/json',
        'Cookie': cookies,
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
      }
    });

    const data = await response.json();
    
    console.log('📊 Status da resposta:', response.status);
    console.log('📋 Sucesso:', data.success);
    
    if (response.status === 200 && data.success) {
      console.log('');
      console.log('🎉 SUCESSO! Dashboard funcionando!');
      console.log('🏢 Dados da empresa:');
      console.log(`   Nome: ${data.data.empresa?.nome_empresa}`);
      console.log(`   Status: ${data.data.empresa?.status}`);
      console.log(`   ID: ${data.data.empresa?.empresa_id}`);
      
    } else if (response.status === 401) {
      console.log('');
      console.log('❌ ERRO REPRODUZIDO!');
      console.log('🔍 Erro:', data.error);
      
      if (data.error === 'Token de autenticação não encontrado') {
        console.log('');
        console.log('🔧 ANÁLISE DO PROBLEMA:');
        console.log('   - Login funcionou corretamente');
        console.log('   - Sessão foi obtida com sucesso');
        console.log('   - Cookies foram simulados corretamente');
        console.log('   - Mas a API não consegue encontrar o token');
        console.log('');
        console.log('💡 POSSÍVEIS CAUSAS:');
        console.log('   1. Formato dos cookies mudou');
        console.log('   2. Nome dos cookies está diferente');
        console.log('   3. API não está lendo os cookies corretamente');
        console.log('   4. Problema na extração do token dos cookies');
      }
      
    } else {
      console.log('❌ Erro inesperado');
      console.log('🔍 Status:', response.status);
      console.log('🔍 Dados:', JSON.stringify(data, null, 2));
    }

    // 5. Testar diferentes formatos de cookies
    if (response.status === 401) {
      console.log('');
      console.log('5️⃣ Testando formatos alternativos de cookies...');
      
      const alternativeFormats = [
        { name: 'Formato 1', cookies: `sb-access-token=${session.access_token}; sb-refresh-token=${session.refresh_token}` },
        { name: 'Formato 2', cookies: `supabase-auth-token=${session.access_token}` },
        { name: 'Formato 3', cookies: `sb-${projectRef}-auth-token.0=${session.access_token}` },
        { name: 'Formato 4', cookies: `auth-token=${session.access_token}` }
      ];
      
      for (const format of alternativeFormats) {
        const testResponse = await fetch('http://localhost:3001/api/proprietario/dashboard/empresa', {
          method: 'GET',
          headers: {
            'Content-Type': 'application/json',
            'Cookie': format.cookies
          }
        });
        
        const testData = await testResponse.json();
        console.log(`   ${format.name} - Status: ${testResponse.status}, Sucesso: ${testData.success}`);
        
        if (testResponse.status === 200) {
          console.log(`   ✅ ${format.name} FUNCIONOU!`);
          break;
        }
      }
    }

    // 6. Fazer logout
    console.log('');
    console.log('6️⃣ Fazendo logout...');
    await supabase.auth.signOut();
    console.log('✅ Logout realizado');

  } catch (error) {
    console.error('❌ Erro inesperado:', error.message);
  }
}

// Executar teste
testLoginDashboardFlow();
