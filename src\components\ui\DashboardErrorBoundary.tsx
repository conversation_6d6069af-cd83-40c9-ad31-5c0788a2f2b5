'use client';

import React, { Component, ErrorInfo, ReactNode } from 'react';
import { motion } from 'framer-motion';
import { MagicCard } from './magic-card';
import { TextAnimate } from './text-animate';
import { ShimmerButton } from './shimmer-button';

interface Props {
  children: ReactNode;
  fallback?: ReactNode;
  onError?: (error: Error, errorInfo: ErrorInfo) => void;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

export class DashboardErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('🚨 Dashboard Error Boundary:', error, errorInfo);
    
    // Log do erro para debug
    console.group('🔍 Error Details');
    console.error('Error:', error.message);
    console.error('Stack:', error.stack);
    console.error('Component Stack:', errorInfo.componentStack);
    console.groupEnd();

    this.setState({ error, errorInfo });
    
    // Callback personalizado para tratamento de erro
    if (this.props.onError) {
      this.props.onError(error, errorInfo);
    }
  }

  handleRetry = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  handleReload = () => {
    window.location.reload();
  };

  render() {
    if (this.state.hasError) {
      // Se há um fallback customizado, usar ele
      if (this.props.fallback) {
        return this.props.fallback;
      }

      // Fallback padrão
      return (
        <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-6">
          <div className="max-w-2xl mx-auto">
            <MagicCard className="p-8 text-center" gradientColor="#EF4444">
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                className="space-y-6"
              >
                {/* Ícone de erro */}
                <motion.div 
                  className="text-6xl"
                  animate={{ rotate: [0, -10, 10, 0] }}
                  transition={{ duration: 2, repeat: Infinity, repeatDelay: 3 }}
                >
                  🚨
                </motion.div>

                {/* Título */}
                <TextAnimate
                  animation="blurInUp"
                  className="text-2xl font-bold text-white"
                >
                  Oops! Algo deu errado
                </TextAnimate>

                {/* Descrição */}
                <div className="text-white/90 space-y-2">
                  <p>
                    Ocorreu um erro inesperado no dashboard. Não se preocupe, 
                    seus dados estão seguros.
                  </p>
                  <p className="text-sm">
                    Tente recarregar a página ou entre em contato com o suporte 
                    se o problema persistir.
                  </p>
                </div>

                {/* Detalhes do erro (apenas em desenvolvimento) */}
                {process.env.NODE_ENV === 'development' && this.state.error && (
                  <motion.div
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: 0.3 }}
                    className="bg-red-900/20 border border-red-500/30 rounded-lg p-4 text-left"
                  >
                    <h4 className="font-medium text-red-200 mb-2">
                      🔍 Detalhes do Erro (Desenvolvimento):
                    </h4>
                    <div className="text-xs text-red-300 space-y-2">
                      <div>
                        <strong>Mensagem:</strong> {this.state.error.message}
                      </div>
                      {this.state.error.stack && (
                        <div>
                          <strong>Stack:</strong>
                          <pre className="mt-1 text-xs overflow-auto max-h-32 bg-red-950/50 p-2 rounded">
                            {this.state.error.stack}
                          </pre>
                        </div>
                      )}
                    </div>
                  </motion.div>
                )}

                {/* Ações */}
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: 0.4 }}
                  className="flex flex-col sm:flex-row gap-4 justify-center"
                >
                  <ShimmerButton 
                    className="bg-white text-red-600 hover:bg-gray-100"
                    shimmerColor="#EF4444"
                    onClick={this.handleRetry}
                  >
                    🔄 Tentar Novamente
                  </ShimmerButton>
                  
                  <ShimmerButton 
                    className="bg-red-600 hover:bg-red-700 text-white"
                    shimmerColor="#FCA5A5"
                    onClick={this.handleReload}
                  >
                    🔃 Recarregar Página
                  </ShimmerButton>
                </motion.div>

                {/* Links de ajuda */}
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.6 }}
                  className="text-sm text-white/70 space-y-2"
                >
                  <p>Precisa de ajuda?</p>
                  <div className="flex flex-col sm:flex-row gap-4 justify-center text-xs">
                    <a 
                      href="/suporte" 
                      className="text-white hover:text-red-200 underline"
                    >
                      📞 Contatar Suporte
                    </a>
                    <a 
                      href="/docs" 
                      className="text-white hover:text-red-200 underline"
                    >
                      📚 Ver Documentação
                    </a>
                    <a 
                      href="/status" 
                      className="text-white hover:text-red-200 underline"
                    >
                      🔍 Status do Sistema
                    </a>
                  </div>
                </motion.div>
              </motion.div>
            </MagicCard>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

// Hook para usar o Error Boundary de forma mais simples
export function useDashboardErrorHandler() {
  const handleError = React.useCallback((error: Error, errorInfo: ErrorInfo) => {
    // Log personalizado ou envio para serviço de monitoramento
    console.error('Dashboard Error:', { error, errorInfo });
    
    // Aqui você pode integrar com serviços como Sentry, LogRocket, etc.
    // Sentry.captureException(error, { contexts: { react: errorInfo } });
  }, []);

  return { handleError };
}

// Componente wrapper para facilitar o uso
interface DashboardErrorWrapperProps {
  children: ReactNode;
  fallback?: ReactNode;
}

export function DashboardErrorWrapper({ 
  children, 
  fallback 
}: DashboardErrorWrapperProps) {
  const { handleError } = useDashboardErrorHandler();

  return (
    <DashboardErrorBoundary onError={handleError} fallback={fallback}>
      {children}
    </DashboardErrorBoundary>
  );
}
