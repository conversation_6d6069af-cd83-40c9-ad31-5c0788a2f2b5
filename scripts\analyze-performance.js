#!/usr/bin/env node

/**
 * Script de Análise de Performance
 * Analisa o código em busca de problemas de performance
 */

const fs = require('fs');
const path = require('path');
const { execSync } = require('child_process');

// Configurações
const CONFIG = {
  srcDir: path.join(__dirname, '../src'),
  excludeDirs: ['node_modules', '.next', 'dist', 'build'],
  fileExtensions: ['.ts', '.tsx', '.js', '.jsx'],
  maxFileSize: 500 * 1024, // 500KB
  maxFunctionLength: 50, // linhas
  maxComponentProps: 10,
  maxHookDependencies: 5
};

// Contadores de problemas
let issues = {
  largeFiles: [],
  longFunctions: [],
  tooManyProps: [],
  missingMemo: [],
  missingCallback: [],
  heavyDependencies: [],
  unusedImports: [],
  duplicatedCode: []
};

// Função para ler arquivos recursivamente
function readFilesRecursively(dir, files = []) {
  const items = fs.readdirSync(dir);
  
  for (const item of items) {
    const fullPath = path.join(dir, item);
    const stat = fs.statSync(fullPath);
    
    if (stat.isDirectory() && !CONFIG.excludeDirs.includes(item)) {
      readFilesRecursively(fullPath, files);
    } else if (stat.isFile() && CONFIG.fileExtensions.some(ext => item.endsWith(ext))) {
      files.push(fullPath);
    }
  }
  
  return files;
}

// Analisar tamanho dos arquivos
function analyzeFileSize(filePath) {
  const stat = fs.statSync(filePath);
  if (stat.size > CONFIG.maxFileSize) {
    issues.largeFiles.push({
      file: path.relative(CONFIG.srcDir, filePath),
      size: Math.round(stat.size / 1024) + 'KB',
      recommendation: 'Considere dividir em arquivos menores'
    });
  }
}

// Analisar funções longas
function analyzeFunctionLength(filePath, content) {
  const lines = content.split('\n');
  let inFunction = false;
  let functionStart = 0;
  let braceCount = 0;
  let functionName = '';
  
  for (let i = 0; i < lines.length; i++) {
    const line = lines[i].trim();
    
    // Detectar início de função
    if (line.match(/^(export\s+)?(async\s+)?function\s+\w+|^const\s+\w+\s*=\s*(async\s+)?\(/)) {
      inFunction = true;
      functionStart = i;
      braceCount = 0;
      functionName = line.match(/function\s+(\w+)|const\s+(\w+)/)?.[1] || 'anonymous';
    }
    
    if (inFunction) {
      braceCount += (line.match(/\{/g) || []).length;
      braceCount -= (line.match(/\}/g) || []).length;
      
      if (braceCount === 0 && line.includes('}')) {
        const functionLength = i - functionStart + 1;
        if (functionLength > CONFIG.maxFunctionLength) {
          issues.longFunctions.push({
            file: path.relative(CONFIG.srcDir, filePath),
            function: functionName,
            lines: functionLength,
            startLine: functionStart + 1,
            recommendation: 'Considere dividir em funções menores'
          });
        }
        inFunction = false;
      }
    }
  }
}

// Analisar props de componentes
function analyzeComponentProps(filePath, content) {
  const componentRegex = /interface\s+(\w+Props)\s*\{([^}]+)\}/g;
  let match;
  
  while ((match = componentRegex.exec(content)) !== null) {
    const propsName = match[1];
    const propsContent = match[2];
    const propCount = (propsContent.match(/^\s*\w+[?]?:/gm) || []).length;
    
    if (propCount > CONFIG.maxComponentProps) {
      issues.tooManyProps.push({
        file: path.relative(CONFIG.srcDir, filePath),
        interface: propsName,
        propCount,
        recommendation: 'Considere agrupar props relacionadas em objetos'
      });
    }
  }
}

// Analisar componentes sem React.memo
function analyzeMissingMemo(filePath, content) {
  const componentRegex = /export\s+(function|const)\s+(\w+)/g;
  const memoRegex = /memo\s*\(/;
  
  let match;
  while ((match = componentRegex.exec(content)) !== null) {
    const componentName = match[2];
    
    // Verificar se é um componente React (começa com maiúscula)
    if (componentName[0] === componentName[0].toUpperCase()) {
      // Verificar se usa memo
      if (!memoRegex.test(content)) {
        issues.missingMemo.push({
          file: path.relative(CONFIG.srcDir, filePath),
          component: componentName,
          recommendation: 'Considere usar React.memo para otimizar re-renderizações'
        });
      }
    }
  }
}

// Analisar hooks sem useCallback
function analyzeMissingCallback(filePath, content) {
  const functionInComponentRegex = /const\s+(\w+)\s*=\s*(async\s+)?\([^)]*\)\s*=>/g;
  const useCallbackRegex = /useCallback\s*\(/;
  
  let match;
  while ((match = functionInComponentRegex.exec(content)) !== null) {
    const functionName = match[1];
    
    // Verificar se é um handler (começa com handle ou on)
    if (functionName.startsWith('handle') || functionName.startsWith('on')) {
      if (!useCallbackRegex.test(content)) {
        issues.missingCallback.push({
          file: path.relative(CONFIG.srcDir, filePath),
          function: functionName,
          recommendation: 'Considere usar useCallback para otimizar performance'
        });
      }
    }
  }
}

// Analisar dependências pesadas de hooks
function analyzeHookDependencies(filePath, content) {
  const hookRegex = /(useEffect|useCallback|useMemo)\s*\([^,]+,\s*\[([^\]]*)\]/g;
  let match;
  
  while ((match = hookRegex.exec(content)) !== null) {
    const hookType = match[1];
    const dependencies = match[2];
    const depCount = dependencies.split(',').filter(dep => dep.trim()).length;
    
    if (depCount > CONFIG.maxHookDependencies) {
      issues.heavyDependencies.push({
        file: path.relative(CONFIG.srcDir, filePath),
        hook: hookType,
        dependencyCount: depCount,
        recommendation: 'Muitas dependências podem causar re-execuções desnecessárias'
      });
    }
  }
}

// Analisar imports não utilizados
function analyzeUnusedImports(filePath, content) {
  const importRegex = /import\s+\{([^}]+)\}\s+from/g;
  let match;
  
  while ((match = importRegex.exec(content)) !== null) {
    const imports = match[1].split(',').map(imp => imp.trim());
    
    for (const importName of imports) {
      const cleanImport = importName.replace(/\s+as\s+\w+/, '');
      const usageRegex = new RegExp(`\\b${cleanImport}\\b`, 'g');
      const usages = (content.match(usageRegex) || []).length;
      
      // Se aparece apenas na linha de import (1 vez), não está sendo usado
      if (usages <= 1) {
        issues.unusedImports.push({
          file: path.relative(CONFIG.srcDir, filePath),
          import: cleanImport,
          recommendation: 'Import não utilizado, considere remover'
        });
      }
    }
  }
}

// Função principal de análise
function analyzeFile(filePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    
    analyzeFileSize(filePath);
    analyzeFunctionLength(filePath, content);
    analyzeComponentProps(filePath, content);
    analyzeMissingMemo(filePath, content);
    analyzeMissingCallback(filePath, content);
    analyzeHookDependencies(filePath, content);
    analyzeUnusedImports(filePath, content);
    
  } catch (error) {
    console.error(`Erro ao analisar ${filePath}:`, error.message);
  }
}

// Gerar relatório
function generateReport() {
  console.log('\n🔍 RELATÓRIO DE ANÁLISE DE PERFORMANCE\n');
  console.log('=====================================\n');
  
  const totalIssues = Object.values(issues).reduce((sum, arr) => sum + arr.length, 0);
  
  if (totalIssues === 0) {
    console.log('✅ Nenhum problema de performance detectado!\n');
    return;
  }
  
  console.log(`⚠️  Total de problemas encontrados: ${totalIssues}\n`);
  
  // Arquivos grandes
  if (issues.largeFiles.length > 0) {
    console.log('📁 ARQUIVOS GRANDES:');
    issues.largeFiles.forEach(issue => {
      console.log(`   • ${issue.file} (${issue.size}) - ${issue.recommendation}`);
    });
    console.log('');
  }
  
  // Funções longas
  if (issues.longFunctions.length > 0) {
    console.log('🔧 FUNÇÕES LONGAS:');
    issues.longFunctions.forEach(issue => {
      console.log(`   • ${issue.file}:${issue.startLine} - ${issue.function} (${issue.lines} linhas)`);
      console.log(`     ${issue.recommendation}`);
    });
    console.log('');
  }
  
  // Muitas props
  if (issues.tooManyProps.length > 0) {
    console.log('⚡ COMPONENTES COM MUITAS PROPS:');
    issues.tooManyProps.forEach(issue => {
      console.log(`   • ${issue.file} - ${issue.interface} (${issue.propCount} props)`);
      console.log(`     ${issue.recommendation}`);
    });
    console.log('');
  }
  
  // Componentes sem memo
  if (issues.missingMemo.length > 0) {
    console.log('🧠 COMPONENTES SEM REACT.MEMO:');
    issues.missingMemo.forEach(issue => {
      console.log(`   • ${issue.file} - ${issue.component}`);
      console.log(`     ${issue.recommendation}`);
    });
    console.log('');
  }
  
  // Funções sem useCallback
  if (issues.missingCallback.length > 0) {
    console.log('🔄 HANDLERS SEM USECALLBACK:');
    issues.missingCallback.forEach(issue => {
      console.log(`   • ${issue.file} - ${issue.function}`);
      console.log(`     ${issue.recommendation}`);
    });
    console.log('');
  }
  
  // Dependências pesadas
  if (issues.heavyDependencies.length > 0) {
    console.log('⚖️  HOOKS COM MUITAS DEPENDÊNCIAS:');
    issues.heavyDependencies.forEach(issue => {
      console.log(`   • ${issue.file} - ${issue.hook} (${issue.dependencyCount} deps)`);
      console.log(`     ${issue.recommendation}`);
    });
    console.log('');
  }
  
  // Imports não utilizados
  if (issues.unusedImports.length > 0) {
    console.log('📦 IMPORTS NÃO UTILIZADOS:');
    issues.unusedImports.forEach(issue => {
      console.log(`   • ${issue.file} - ${issue.import}`);
    });
    console.log('');
  }
  
  // Recomendações gerais
  console.log('💡 RECOMENDAÇÕES GERAIS:');
  console.log('   • Use React.memo em componentes que recebem props complexas');
  console.log('   • Use useCallback para funções passadas como props');
  console.log('   • Use useMemo para cálculos custosos');
  console.log('   • Mantenha funções pequenas e focadas');
  console.log('   • Remova imports não utilizados');
  console.log('   • Considere code splitting para arquivos grandes');
  console.log('');
}

// Executar análise
function main() {
  console.log('🚀 Iniciando análise de performance...\n');
  
  const files = readFilesRecursively(CONFIG.srcDir);
  console.log(`📊 Analisando ${files.length} arquivos...\n`);
  
  files.forEach(analyzeFile);
  generateReport();
  
  console.log('✨ Análise concluída!\n');
}

// Executar se chamado diretamente
if (require.main === module) {
  main();
}

module.exports = { main, analyzeFile, generateReport };
