# 📚 DOCUMENTAÇÃO COMPLETA DO BANCO DE DADOS SERVICETECH

## Versão: 2.0 (Pós-Migração)
## Data: 18/06/2025

---

## 1. 📋 ESQUEMA COMPLETO DAS TABELAS

### 1.1 TABELAS NOVAS (Criadas na Migração)

#### **`roles`** - Definição de Papéis do Sistema
```sql
CREATE TABLE roles (
    role_id SERIAL PRIMARY KEY,
    nome_papel VARCHAR(50) NOT NULL UNIQUE,
    descricao TEXT,
    is_global BOOLEAN DEFAULT false,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Colunas:**
- `role_id` (SERIAL, PK): Identificador único do papel
- `nome_papel` (VARCHAR(50), UNIQUE): Nome do papel (Administrador, Usuário, Proprietário, Colaborador)
- `descricao` (TEXT): Descrição detalhada do papel
- `is_global` (BOOLEAN): true para papéis globais (Admin/Usuário), false para específicos por empresa
- `created_at`, `updated_at` (TIMESTAMPTZ): Timestamps de auditoria

**Dados Atuais:**
```
role_id | nome_papel    | is_global
--------|---------------|----------
1       | Administrador | true
2       | Usuário       | true  
3       | Proprietário  | false
4       | Colaborador   | false
```

#### **`user_roles`** - Associação Usuário-Papel-Empresa
```sql
CREATE TABLE user_roles (
    user_role_id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id) ON DELETE CASCADE,
    role_id INTEGER NOT NULL REFERENCES roles(role_id) ON DELETE CASCADE,
    empresa_id INTEGER REFERENCES empresas(empresa_id) ON DELETE CASCADE,
    ativo BOOLEAN DEFAULT true,
    data_atribuicao TIMESTAMPTZ DEFAULT NOW(),
    data_expiracao TIMESTAMPTZ,
    atribuido_por UUID REFERENCES auth.users(id),
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

**Colunas:**
- `user_role_id` (SERIAL, PK): Identificador único da associação
- `user_id` (UUID, FK): Referência ao usuário em auth.users
- `role_id` (INTEGER, FK): Referência ao papel em roles
- `empresa_id` (INTEGER, FK): Referência à empresa (NULL para papéis globais)
- `ativo` (BOOLEAN): Status da associação
- `data_atribuicao` (TIMESTAMPTZ): Quando o papel foi atribuído
- `data_expiracao` (TIMESTAMPTZ): Expiração do papel (opcional)
- `atribuido_por` (UUID, FK): Quem atribuiu o papel

#### **`convites_colaborador`** - Gestão de Convites
```sql
CREATE TABLE convites_colaborador (
    convite_id SERIAL PRIMARY KEY,
    empresa_id INTEGER NOT NULL REFERENCES empresas(empresa_id) ON DELETE CASCADE,
    email_convidado VARCHAR(255) NOT NULL,
    token_convite VARCHAR(255) NOT NULL UNIQUE,
    criado_por UUID NOT NULL REFERENCES auth.users(id),
    status_convite VARCHAR(20) DEFAULT 'pendente' 
        CHECK (status_convite IN ('pendente', 'aceito', 'expirado', 'cancelado')),
    data_expiracao TIMESTAMPTZ NOT NULL DEFAULT (NOW() + INTERVAL '24 hours'),
    data_aceite TIMESTAMPTZ,
    user_id_aceite UUID REFERENCES auth.users(id),
    observacoes TEXT,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);
```

### 1.2 TABELAS MODIFICADAS (Existentes + Novas Colunas)

#### **`empresas`** - Informações das Empresas
**Colunas Originais Mantidas:**
- `empresa_id`, `proprietario_user_id`, `nome_empresa`, `cnpj`, `telefone`
- `endereco`, `numero`, `complemento`, `bairro`, `cidade`, `estado`, `cep`
- `descricao`, `logo_url`, `fotos_portfolio_urls`, `horario_funcionamento`
- `plano_saas_id`, `stripe_customer_id`, `slug`, `segmento`, `status`
- `politica_cancelamento`, `modelo_negocio`, `created_at`, `updated_at`

**Novas Colunas Adicionadas:**
```sql
-- Contatos adicionais
website VARCHAR(255)
instagram VARCHAR(255)  
whatsapp VARCHAR(20)
email_contato VARCHAR(255)

-- Configurações operacionais
configuracao_notificacoes JSONB DEFAULT '{"email": true, "push": true, "sms": false}'
configuracao_agendamento JSONB DEFAULT '{"confirmacao_automatica": false, "prazo_confirmacao_horas": 24}'
dias_funcionamento JSONB DEFAULT '["segunda", "terca", "quarta", "quinta", "sexta", "sabado"]'
timezone VARCHAR(50) DEFAULT 'America/Sao_Paulo'
```

#### **`colaboradores_empresa`** - Associações Colaborador-Empresa
**Novas Colunas Adicionadas:**
```sql
papel_empresa VARCHAR(50) DEFAULT 'colaborador' 
    CHECK (papel_empresa IN ('proprietario', 'colaborador', 'gerente'))
data_inicio_trabalho DATE
data_fim_trabalho DATE
observacoes_internas TEXT
configuracao_notificacoes JSONB DEFAULT '{"email": true, "push": true, "sms": false}'
```

#### **`agendamentos`** - Sistema de Agendamentos
**Novas Colunas Adicionadas:**
```sql
tipo_agendamento VARCHAR(20) DEFAULT 'servico' 
    CHECK (tipo_agendamento IN ('servico', 'combo', 'assinatura'))
combo_id INTEGER REFERENCES combos_servicos(combo_id)
assinatura_cliente_id INTEGER REFERENCES assinaturas_servico_cliente(assinatura_id)
observacoes_empresa TEXT
avaliacao_cliente INTEGER CHECK (avaliacao_cliente >= 1 AND avaliacao_cliente <= 5)
comentario_avaliacao TEXT
data_avaliacao TIMESTAMPTZ
```

#### **`servicos`** - Catálogo de Serviços
**Novas Colunas Adicionadas:**
```sql
imagem_url TEXT
tags TEXT[]
ordem_exibicao INTEGER DEFAULT 0
disponivel_online BOOLEAN DEFAULT true
preparacao_minutos INTEGER DEFAULT 0
limpeza_minutos INTEGER DEFAULT 0
```

#### **`notificacoes`** - Sistema de Notificações
**Novas Colunas Adicionadas:**
```sql
template_id VARCHAR(100)
parametros_template JSONB
prioridade VARCHAR(20) DEFAULT 'normal' 
    CHECK (prioridade IN ('baixa', 'normal', 'alta', 'urgente'))
agrupamento_id VARCHAR(100)
acao_url TEXT
expira_em TIMESTAMPTZ
```

### 1.3 TABELAS RENOMEADAS

#### **`assinaturas_servico_cliente`** (anteriormente `planos_servico_cliente`)
- Coluna `plano_cliente_id` renomeada para `assinatura_id`
- Estrutura interna mantida
- Relacionamentos atualizados

### 1.4 TABELAS ORIGINAIS INALTERADAS

- `assinaturas_saas_empresas`
- `colaborador_servicos`
- `combo_itens`
- `combos_servicos`
- `onboarding_drafts`
- `pagamentos`
- `papeis` (mantida para compatibilidade)
- `planos_saas`

---

## 2. 🔗 RELACIONAMENTOS E INTEGRIDADE

### 2.1 Diagrama de Relacionamentos Principais

```
auth.users (Supabase Auth)
    ↓ (1:N)
user_roles ←→ roles
    ↓ (N:1)
empresas
    ↓ (1:N)
├── colaboradores_empresa
├── servicos
├── agendamentos
├── convites_colaborador
├── combos_servicos
└── assinaturas_saas_empresas
```

### 2.2 Foreign Keys Detalhadas

#### **Relacionamentos Centrais:**
```sql
-- user_roles
user_id → auth.users(id)
role_id → roles(role_id)
empresa_id → empresas(empresa_id)
atribuido_por → auth.users(id)

-- convites_colaborador
empresa_id → empresas(empresa_id)
criado_por → auth.users(id)
user_id_aceite → auth.users(id)

-- agendamentos (novos relacionamentos)
combo_id → combos_servicos(combo_id)
assinatura_cliente_id → assinaturas_servico_cliente(assinatura_id)
```

### 2.3 Estrutura Multi-Tenant

**Implementação:**
- **Tenant ID**: `empresa_id` serve como identificador do tenant
- **Isolamento**: Políticas RLS garantem acesso apenas aos dados da empresa
- **Papéis Hierárquicos**: 
  - Administrador: Acesso global
  - Proprietário: Acesso à sua empresa
  - Colaborador: Acesso limitado à sua empresa
  - Usuário: Acesso aos próprios dados

**Fluxo de Acesso:**
```
1. Usuário autentica → auth.uid()
2. Sistema consulta user_roles para obter papel e empresa
3. RLS aplica filtros baseados no papel e empresa_id
4. Dados retornados respeitam isolamento multi-tenant
```

---

## 3. 👥 SISTEMA DE PAPÉIS E PERMISSÕES

### 3.1 Hierarquia de Papéis

```
Administrador (Global)
    ├── Acesso total ao sistema
    ├── Gerencia todos os planos SaaS
    └── Visualiza relatórios agregados

Proprietário (Por Empresa)
    ├── Gerencia sua empresa
    ├── Convida/remove colaboradores
    ├── Define serviços e horários
    └── Acessa relatórios da empresa

Colaborador (Por Empresa)
    ├── Gerencia própria agenda
    ├── Confirma/cancela agendamentos
    └── Acesso limitado a relatórios

Usuário (Global)
    ├── Busca empresas
    ├── Realiza agendamentos
    └── Gerencia próprio perfil
```

### 3.2 Diferenças entre Papéis Globais e Específicos

#### **Papéis Globais** (`is_global = true`)
- **Administrador**: Acesso irrestrito a todo o sistema
- **Usuário**: Papel padrão, acesso básico

**Características:**
- `empresa_id = NULL` na tabela `user_roles`
- Aplicam-se independente da empresa
- Não expiram automaticamente

#### **Papéis Específicos por Empresa** (`is_global = false`)
- **Proprietário**: Vinculado a uma empresa específica
- **Colaborador**: Vinculado a uma empresa específica

**Características:**
- `empresa_id` obrigatório na tabela `user_roles`
- Limitados ao contexto da empresa
- Podem ter data de expiração

### 3.3 Políticas RLS Implementadas

#### **Tabela `roles`**
```sql
-- Leitura pública
"Roles são visíveis para todos" 
FOR SELECT USING (true)

-- Modificação restrita
"Apenas administradores podem modificar roles"
FOR ALL USING (user_has_admin_role())
```

#### **Tabela `user_roles`**
```sql
-- Usuários veem próprios papéis
"Usuários podem ver seus próprios papéis"
FOR SELECT USING (user_id = auth.uid())

-- Proprietários gerenciam sua empresa
"Proprietários podem gerenciar papéis de sua empresa"
FOR ALL USING (user_is_owner_of_company(empresa_id))

-- Administradores acesso total
"Administradores podem gerenciar todos os papéis"
FOR ALL USING (user_has_admin_role())
```

#### **Tabela `convites_colaborador`**
```sql
-- Proprietários gerenciam convites
"Proprietários podem gerenciar convites de sua empresa"
FOR ALL USING (user_is_owner_of_company(empresa_id))

-- Convidados veem seus convites
"Usuários podem ver convites direcionados a eles"
FOR SELECT USING (user_email_matches(email_convidado))

-- Convidados aceitam convites
"Usuários podem aceitar convites direcionados a eles"
FOR UPDATE USING (user_can_accept_invite())
```

---

## 4. 📈 ÍNDICES E PERFORMANCE

### 4.1 Índices das Novas Tabelas

#### **`user_roles`**
```sql
idx_user_roles_user_id ON user_roles(user_id)
idx_user_roles_empresa_id ON user_roles(empresa_id)  
idx_user_roles_ativo ON user_roles(ativo) WHERE ativo = true
```

#### **`convites_colaborador`**
```sql
idx_convites_token ON convites_colaborador(token_convite)
idx_convites_email ON convites_colaborador(email_convidado)
idx_convites_status ON convites_colaborador(status_convite)
idx_convites_expiracao ON convites_colaborador(data_expiracao)
```

### 4.2 Índices Adicionais Criados

#### **`empresas`**
```sql
idx_empresas_slug ON empresas(slug)           -- Busca por URL única
idx_empresas_cidade ON empresas(cidade)       -- Filtro geográfico
idx_empresas_status ON empresas(status)       -- Empresas ativas
idx_empresas_segmento ON empresas(segmento)   -- Filtro por categoria
```

#### **`agendamentos`**
```sql
idx_agendamentos_data_inicio ON agendamentos(data_hora_inicio)
idx_agendamentos_status ON agendamentos(status_agendamento)
idx_agendamentos_empresa_data ON agendamentos(empresa_id, data_hora_inicio)
idx_agendamentos_colaborador_data ON agendamentos(colaborador_user_id, data_hora_inicio)
idx_agendamentos_cliente_data ON agendamentos(cliente_user_id, data_hora_inicio)
```

#### **`notificacoes`**
```sql
idx_notificacoes_user_lida ON notificacoes(user_id, lida)
idx_notificacoes_tipo ON notificacoes(tipo_notificacao)
idx_notificacoes_agrupamento ON notificacoes(agrupamento_id)
```

### 4.3 Propósito dos Índices

| Índice | Propósito | Benefício |
|--------|-----------|-----------|
| `idx_user_roles_user_id` | Busca rápida de papéis por usuário | Autenticação e autorização |
| `idx_empresas_cidade` | Filtro geográfico no marketplace | Busca de empresas por localização |
| `idx_agendamentos_empresa_data` | Agenda da empresa por período | Dashboard e relatórios |
| `idx_notificacoes_user_lida` | Notificações não lidas | Interface do usuário |

---

## 5. ⚙️ FUNÇÕES E TRIGGERS

### 5.1 Funções Criadas

#### **`update_updated_at_column()`**
```sql
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;
```
**Propósito**: Atualiza automaticamente o campo `updated_at` em modificações

#### **`get_user_role_in_company(user_uuid UUID, company_id INTEGER)`**
```sql
RETURNS TEXT
```
**Propósito**: Retorna o papel do usuário em uma empresa específica
**Lógica**: Prioriza papéis específicos da empresa sobre papéis globais

#### **`user_has_permission_in_company(user_uuid UUID, company_id INTEGER, required_role TEXT)`**
```sql
RETURNS BOOLEAN
```
**Propósito**: Verifica se usuário tem permissão específica em uma empresa
**Hierarquia**: Administrador > Proprietário > Colaborador > Usuário

### 5.2 Triggers Implementados

#### **Triggers de Auditoria**
```sql
-- Atualização automática de timestamps
update_roles_updated_at ON roles
update_user_roles_updated_at ON user_roles  
update_convites_updated_at ON convites_colaborador
```

**Funcionamento**: Executam `BEFORE UPDATE` para manter auditoria de modificações

---

## 6. 📊 DADOS MIGRADOS

### 6.1 Usuários e Papéis Atuais

```sql
-- Estado atual dos usuários após migração
SELECT 
    u.email,
    r.nome_papel,
    ur.empresa_id,
    e.nome_empresa
FROM user_roles ur
JOIN auth.users u ON ur.user_id = u.id
JOIN roles r ON ur.role_id = r.role_id
LEFT JOIN empresas e ON ur.empresa_id = e.empresa_id
WHERE ur.ativo = true;
```

**Resultado:**
| Email | Papel | Empresa | Nome da Empresa |
|-------|-------|---------|-----------------|
| <EMAIL> | Administrador | NULL | - |
| <EMAIL> | Usuário | NULL | - |
| <EMAIL> | Colaborador | NULL | - |
| <EMAIL> | Proprietário | 8 | Salão Maria Santos |
| <EMAIL> | Proprietário | 3 | Barbearia Santos |
| <EMAIL> | Proprietário | 1 | Empresa Teste LTDA |
| <EMAIL> | Usuário | NULL | - |

### 6.2 Preservação de Dados

#### **Dados Mantidos Intactos:**
- ✅ **4 empresas** com todas as informações
- ✅ **8 serviços** com preços e durações
- ✅ **1 colaborador** com associação preservada
- ✅ **Relacionamentos** entre usuários e empresas
- ✅ **Configurações** de planos SaaS

#### **Dados Migrados:**
- ✅ **7 usuários** transferidos para nova estrutura de papéis
- ✅ **Papéis** baseados em `raw_user_meta_data` migrados
- ✅ **Associações** empresa-proprietário preservadas

### 6.3 Configurações Padrão Aplicadas

#### **Empresas:**
```json
{
  "configuracao_notificacoes": {
    "email": true,
    "push": true, 
    "sms": false
  },
  "configuracao_agendamento": {
    "confirmacao_automatica": false,
    "prazo_confirmacao_horas": 24
  },
  "dias_funcionamento": [
    "segunda", "terca", "quarta", 
    "quinta", "sexta", "sabado"
  ],
  "timezone": "America/Sao_Paulo"
}
```

#### **Colaboradores:**
```json
{
  "papel_empresa": "colaborador",
  "configuracao_notificacoes": {
    "email": true,
    "push": true,
    "sms": false
  }
}
```

---

## 7. 🔄 COMPATIBILIDADE E MIGRAÇÃO

### 7.1 Compatibilidade com Código Existente

#### **Tabelas Inalteradas:**
- Queries existentes continuam funcionando
- APIs mantêm compatibilidade
- Relacionamentos preservados

#### **Novas Funcionalidades Disponíveis:**
- Sistema de convites para colaboradores
- Configurações avançadas de notificação
- Suporte a combos e assinaturas em agendamentos
- Sistema de avaliações

### 7.2 Estratégia de Rollback

O arquivo `database_migration_plan.sql` inclui seção completa de rollback que:
- Remove todas as novas colunas
- Exclui novas tabelas
- Restaura nomes originais
- Preserva dados existentes

---

## 8. 📋 RESUMO TÉCNICO

### 8.1 Estatísticas da Migração

| Métrica | Valor |
|---------|-------|
| Tabelas Criadas | 3 |
| Colunas Adicionadas | 32 |
| Funções Criadas | 3 |
| Políticas RLS | 4 |
| Índices Criados | 15 |
| Usuários Migrados | 7 |
| Dados Preservados | 100% |

### 8.2 Benefícios Alcançados

1. **Segurança Aprimorada**: RLS granular por empresa
2. **Escalabilidade**: Estrutura multi-tenant robusta
3. **Flexibilidade**: Sistema de papéis configurável
4. **Performance**: Índices otimizados para consultas frequentes
5. **Auditoria**: Timestamps automáticos e rastreabilidade
6. **Funcionalidades**: Suporte completo aos requisitos do `lr raiz.md`

### 8.3 Próximos Passos Recomendados

1. **Atualizar tipos TypeScript** para novas estruturas
2. **Implementar hooks** para gestão de papéis
3. **Criar interfaces** para sistema de convites
4. **Desenvolver dashboards** com novas métricas
5. **Implementar testes** para validar segurança RLS

---

*Documentação gerada em 18/06/2025 - ServiceTech Database v2.0*
