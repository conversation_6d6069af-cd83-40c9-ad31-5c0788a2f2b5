import { SectionConfig } from '@/components/ui/dashboard-sections-advanced';

// Configurações das seções do dashboard
export const DASHBOARD_SECTIONS: Record<string, SectionConfig> = {
  'informacoes-empresa': {
    id: 'informacoes-empresa',
    title: 'Informações da Empresa',
    description: 'Dados principais, configurações e status da sua empresa',
    icon: '🏢',
    gradientColor: '#3B82F6',
    priority: 'high',
    category: 'business',
    refreshInterval: 5 * 60 * 1000, // 5 minutos
    collapsible: false,
    defaultExpanded: true
  },

  'metricas-negocio': {
    id: 'metricas-negocio',
    title: 'Métricas de Negócio',
    description: 'Indicadores de performance, crescimento e análise financeira',
    icon: '📊',
    gradientColor: '#10B981',
    priority: 'high',
    category: 'business',
    refreshInterval: 2 * 60 * 1000, // 2 minutos
    collapsible: true,
    defaultExpanded: true
  },

  'status-plano-saas': {
    id: 'status-plano-saas',
    title: 'Status do Plano SaaS',
    description: 'Informações sobre assinatura, uso e limites do seu plano',
    icon: '💎',
    gradientColor: '#8B5CF6',
    priority: 'medium',
    category: 'administrative',
    refreshInterval: 10 * 60 * 1000, // 10 minutos
    collapsible: true,
    defaultExpanded: true
  },

  'alertas-dashboard': {
    id: 'alertas-dashboard',
    title: 'Alertas e Notificações',
    description: 'Avisos importantes, notificações e alertas do sistema',
    icon: '🔔',
    gradientColor: '#F59E0B',
    priority: 'medium',
    category: 'technical',
    refreshInterval: 1 * 60 * 1000, // 1 minuto
    collapsible: true,
    defaultExpanded: true
  },

  'acoes-rapidas': {
    id: 'acoes-rapidas',
    title: 'Ações Rápidas',
    description: 'Funcionalidades mais utilizadas e atalhos para tarefas frequentes',
    icon: '⚡',
    gradientColor: '#EF4444',
    priority: 'low',
    category: 'technical',
    refreshInterval: 5 * 60 * 1000, // 5 minutos
    collapsible: true,
    defaultExpanded: true
  },

  // Seções adicionais que podem ser implementadas
  'relatorios-rapidos': {
    id: 'relatorios-rapidos',
    title: 'Relatórios Rápidos',
    description: 'Visualizações e relatórios essenciais para tomada de decisão',
    icon: '📈',
    gradientColor: '#06B6D4',
    priority: 'medium',
    category: 'business',
    refreshInterval: 15 * 60 * 1000, // 15 minutos
    collapsible: true,
    defaultExpanded: false
  },

  'integracoes': {
    id: 'integracoes',
    title: 'Integrações',
    description: 'Status e configurações das integrações com sistemas externos',
    icon: '🔗',
    gradientColor: '#84CC16',
    priority: 'low',
    category: 'technical',
    refreshInterval: 30 * 60 * 1000, // 30 minutos
    collapsible: true,
    defaultExpanded: false
  },

  'seguranca': {
    id: 'seguranca',
    title: 'Segurança',
    description: 'Monitoramento de segurança, logs e atividades suspeitas',
    icon: '🛡️',
    gradientColor: '#DC2626',
    priority: 'high',
    category: 'technical',
    refreshInterval: 5 * 60 * 1000, // 5 minutos
    collapsible: true,
    defaultExpanded: false
  }
};

// Configurações de layout das seções
export const SECTION_LAYOUTS = {
  // Layout padrão - todas as seções principais
  default: [
    'informacoes-empresa',
    'metricas-negocio',
    'status-plano-saas',
    {
      type: 'grid',
      columns: 2,
      sections: ['alertas-dashboard', 'acoes-rapidas']
    }
  ],

  // Layout completo - inclui seções adicionais
  complete: [
    'informacoes-empresa',
    'metricas-negocio',
    {
      type: 'grid',
      columns: 2,
      sections: ['status-plano-saas', 'relatorios-rapidos']
    },
    {
      type: 'grid',
      columns: 3,
      sections: ['alertas-dashboard', 'acoes-rapidas', 'integracoes']
    },
    'seguranca'
  ],

  // Layout compacto - apenas essenciais
  compact: [
    'informacoes-empresa',
    {
      type: 'grid',
      columns: 2,
      sections: ['metricas-negocio', 'status-plano-saas']
    },
    {
      type: 'grid',
      columns: 2,
      sections: ['alertas-dashboard', 'acoes-rapidas']
    }
  ],

  // Layout focado em negócio
  business: [
    'informacoes-empresa',
    'metricas-negocio',
    'relatorios-rapidos',
    {
      type: 'grid',
      columns: 2,
      sections: ['status-plano-saas', 'acoes-rapidas']
    }
  ],

  // Layout técnico
  technical: [
    'informacoes-empresa',
    {
      type: 'grid',
      columns: 2,
      sections: ['alertas-dashboard', 'seguranca']
    },
    {
      type: 'grid',
      columns: 2,
      sections: ['integracoes', 'acoes-rapidas']
    },
    'metricas-negocio'
  ]
};

// Configurações de personalização por usuário
export interface UserDashboardPreferences {
  layout: keyof typeof SECTION_LAYOUTS;
  hiddenSections: string[];
  sectionOrder: string[];
  refreshIntervals: Record<string, number>;
  collapsedSections: string[];
}

// Configurações padrão do usuário
export const DEFAULT_USER_PREFERENCES: UserDashboardPreferences = {
  layout: 'default',
  hiddenSections: [],
  sectionOrder: [],
  refreshIntervals: {},
  collapsedSections: []
};

// Utilitários para gerenciar configurações
export class DashboardConfigManager {
  private static STORAGE_KEY = 'dashboard-preferences';

  static getUserPreferences(): UserDashboardPreferences {
    if (typeof window === 'undefined') return DEFAULT_USER_PREFERENCES;
    
    try {
      const stored = localStorage.getItem(this.STORAGE_KEY);
      if (stored) {
        const parsed = JSON.parse(stored);
        return { ...DEFAULT_USER_PREFERENCES, ...parsed };
      }
    } catch (error) {
      console.warn('Erro ao carregar preferências do dashboard:', error);
    }
    
    return DEFAULT_USER_PREFERENCES;
  }

  static saveUserPreferences(preferences: Partial<UserDashboardPreferences>): void {
    if (typeof window === 'undefined') return;
    
    try {
      const current = this.getUserPreferences();
      const updated = { ...current, ...preferences };
      localStorage.setItem(this.STORAGE_KEY, JSON.stringify(updated));
    } catch (error) {
      console.warn('Erro ao salvar preferências do dashboard:', error);
    }
  }

  static getSectionConfig(sectionId: string): SectionConfig | null {
    return DASHBOARD_SECTIONS[sectionId] || null;
  }

  static getLayoutSections(layout: keyof typeof SECTION_LAYOUTS): string[] {
    const layoutConfig = SECTION_LAYOUTS[layout];
    const sections: string[] = [];

    layoutConfig.forEach(item => {
      if (typeof item === 'string') {
        sections.push(item);
      } else if (item.type === 'grid') {
        sections.push(...item.sections);
      }
    });

    return sections;
  }

  static getVisibleSections(preferences: UserDashboardPreferences): string[] {
    const layoutSections = this.getLayoutSections(preferences.layout);
    return layoutSections.filter(sectionId => 
      !preferences.hiddenSections.includes(sectionId)
    );
  }

  static getSectionsByCategory(category: SectionConfig['category']): SectionConfig[] {
    return Object.values(DASHBOARD_SECTIONS).filter(
      section => section.category === category
    );
  }

  static getSectionsByPriority(priority: SectionConfig['priority']): SectionConfig[] {
    return Object.values(DASHBOARD_SECTIONS).filter(
      section => section.priority === priority
    );
  }

  static resetPreferences(): void {
    if (typeof window === 'undefined') return;
    localStorage.removeItem(this.STORAGE_KEY);
  }
}

// Hook para usar as configurações do dashboard
export function useDashboardConfig() {
  const [preferences, setPreferences] = React.useState<UserDashboardPreferences>(
    DashboardConfigManager.getUserPreferences()
  );

  const updatePreferences = React.useCallback((updates: Partial<UserDashboardPreferences>) => {
    const newPreferences = { ...preferences, ...updates };
    setPreferences(newPreferences);
    DashboardConfigManager.saveUserPreferences(updates);
  }, [preferences]);

  const toggleSection = React.useCallback((sectionId: string) => {
    const isHidden = preferences.hiddenSections.includes(sectionId);
    const hiddenSections = isHidden
      ? preferences.hiddenSections.filter(id => id !== sectionId)
      : [...preferences.hiddenSections, sectionId];
    
    updatePreferences({ hiddenSections });
  }, [preferences.hiddenSections, updatePreferences]);

  const toggleCollapsed = React.useCallback((sectionId: string) => {
    const isCollapsed = preferences.collapsedSections.includes(sectionId);
    const collapsedSections = isCollapsed
      ? preferences.collapsedSections.filter(id => id !== sectionId)
      : [...preferences.collapsedSections, sectionId];
    
    updatePreferences({ collapsedSections });
  }, [preferences.collapsedSections, updatePreferences]);

  const resetToDefaults = React.useCallback(() => {
    setPreferences(DEFAULT_USER_PREFERENCES);
    DashboardConfigManager.resetPreferences();
  }, []);

  return {
    preferences,
    updatePreferences,
    toggleSection,
    toggleCollapsed,
    resetToDefaults,
    getSectionConfig: DashboardConfigManager.getSectionConfig,
    getVisibleSections: () => DashboardConfigManager.getVisibleSections(preferences)
  };
}

// Adicionar import do React para o hook
import React from 'react';
