'use client';

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MagicCard } from './magic-card';

interface Notification {
  id: string;
  type: 'success' | 'warning' | 'info' | 'error';
  title: string;
  message: string;
  duration?: number;
  persistent?: boolean;
  action?: {
    text: string;
    onClick: () => void;
  };
}

interface DashboardNotificationsProps {
  className?: string;
}

const notificationIcons = {
  success: '✅',
  warning: '⚠️',
  info: 'ℹ️',
  error: '❌'
};

const notificationColors = {
  success: '#10B981',
  warning: '#F59E0B',
  info: '#3B82F6',
  error: '#EF4444'
};

export function DashboardNotifications({ className = '' }: DashboardNotificationsProps) {
  const [notifications, setNotifications] = useState<Notification[]>([]);

  // Função para adicionar notificação
  const addNotification = (notification: Omit<Notification, 'id'>) => {
    const id = Date.now().toString();
    const newNotification = { ...notification, id };
    
    setNotifications(prev => [...prev, newNotification]);

    // Auto-remover após duração especificada (padrão: 5 segundos)
    if (!notification.persistent) {
      setTimeout(() => {
        removeNotification(id);
      }, notification.duration || 5000);
    }
  };

  // Função para remover notificação
  const removeNotification = (id: string) => {
    setNotifications(prev => prev.filter(n => n.id !== id));
  };

  // Expor funções globalmente para uso em outros componentes
  useEffect(() => {
    (window as any).dashboardNotify = {
      success: (title: string, message: string, options?: Partial<Notification>) =>
        addNotification({ type: 'success', title, message, ...options }),
      warning: (title: string, message: string, options?: Partial<Notification>) =>
        addNotification({ type: 'warning', title, message, ...options }),
      info: (title: string, message: string, options?: Partial<Notification>) =>
        addNotification({ type: 'info', title, message, ...options }),
      error: (title: string, message: string, options?: Partial<Notification>) =>
        addNotification({ type: 'error', title, message, ...options }),
    };

    return () => {
      delete (window as any).dashboardNotify;
    };
  }, []);

  if (notifications.length === 0) return null;

  return (
    <div className={`fixed top-4 right-4 z-50 space-y-3 max-w-sm ${className}`}>
      <AnimatePresence>
        {notifications.map((notification) => (
          <motion.div
            key={notification.id}
            initial={{ opacity: 0, x: 300, scale: 0.8 }}
            animate={{ opacity: 1, x: 0, scale: 1 }}
            exit={{ opacity: 0, x: 300, scale: 0.8 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
          >
            <MagicCard 
              className="p-4 shadow-lg border-l-4"
              gradientColor={notificationColors[notification.type]}
              style={{ borderLeftColor: notificationColors[notification.type] }}
            >
              <div className="flex items-start space-x-3">
                {/* Ícone */}
                <div className="text-xl flex-shrink-0">
                  {notificationIcons[notification.type]}
                </div>

                {/* Conteúdo */}
                <div className="flex-1 min-w-0">
                  <h4 className="font-medium text-gray-900 dark:text-white text-sm">
                    {notification.title}
                  </h4>
                  <p className="text-gray-600 dark:text-gray-300 text-xs mt-1">
                    {notification.message}
                  </p>

                  {/* Ação */}
                  {notification.action && (
                    <button
                      onClick={notification.action.onClick}
                      className="mt-2 text-xs font-medium text-blue-600 hover:text-blue-700 dark:text-blue-400 dark:hover:text-blue-300"
                    >
                      {notification.action.text}
                    </button>
                  )}
                </div>

                {/* Botão de fechar */}
                <button
                  onClick={() => removeNotification(notification.id)}
                  className="text-gray-400 hover:text-gray-600 dark:text-gray-500 dark:hover:text-gray-300 flex-shrink-0"
                >
                  <svg className="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                    <path fillRule="evenodd" d="M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z" clipRule="evenodd" />
                  </svg>
                </button>
              </div>
            </MagicCard>
          </motion.div>
        ))}
      </AnimatePresence>
    </div>
  );
}

// Hook para usar notificações de forma mais simples
export function useDashboardNotifications() {
  const notify = {
    success: (title: string, message: string, options?: Partial<Notification>) => {
      if ((window as any).dashboardNotify) {
        (window as any).dashboardNotify.success(title, message, options);
      }
    },
    warning: (title: string, message: string, options?: Partial<Notification>) => {
      if ((window as any).dashboardNotify) {
        (window as any).dashboardNotify.warning(title, message, options);
      }
    },
    info: (title: string, message: string, options?: Partial<Notification>) => {
      if ((window as any).dashboardNotify) {
        (window as any).dashboardNotify.info(title, message, options);
      }
    },
    error: (title: string, message: string, options?: Partial<Notification>) => {
      if ((window as any).dashboardNotify) {
        (window as any).dashboardNotify.error(title, message, options);
      }
    }
  };

  return { notify };
}

// Notificações pré-configuradas para casos comuns do dashboard
export function useDashboardStatusNotifications() {
  const { notify } = useDashboardNotifications();

  const notifyDataLoaded = () => {
    notify.success(
      'Dashboard Carregado',
      'Todos os dados foram carregados com sucesso!'
    );
  };

  const notifyDataError = (error: string) => {
    notify.error(
      'Erro ao Carregar Dados',
      `Ocorreu um erro: ${error}`,
      { 
        persistent: true,
        action: {
          text: 'Tentar Novamente',
          onClick: () => window.location.reload()
        }
      }
    );
  };

  const notifyNoCompany = () => {
    notify.warning(
      'Empresa Não Configurada',
      'Configure sua empresa para acessar todas as funcionalidades.',
      {
        persistent: true,
        action: {
          text: 'Configurar Agora',
          onClick: () => window.location.href = '/proprietario/empresa/criar'
        }
      }
    );
  };

  const notifyConfigurationIncomplete = (percentage: number) => {
    notify.info(
      'Configuração Incompleta',
      `Sua empresa está ${percentage}% configurada. Complete para aproveitar todos os recursos.`,
      {
        action: {
          text: 'Completar',
          onClick: () => window.location.href = '/proprietario/empresa'
        }
      }
    );
  };

  return {
    notifyDataLoaded,
    notifyDataError,
    notifyNoCompany,
    notifyConfigurationIncomplete
  };
}
