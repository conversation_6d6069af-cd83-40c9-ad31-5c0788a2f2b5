/**
 * Testes para o componente CardAgendamento
 */

import React from 'react';
import { render, screen, fireEvent } from '@testing-library/react';
import '@testing-library/jest-dom';
import { CardAgendamento } from '../CardAgendamento';
import { AgendamentoCompleto } from '@/types/agendamentos';

// Mock do contexto de autenticação
jest.mock('@/contexts/AuthContext', () => ({
  useAuth: () => ({
    user: {
      id: 'user-1',
      role: 'Proprietario'
    }
  })
}));

// Mock do componente BotaoCancelamento
jest.mock('../BotaoCancelamento', () => ({
  BotaoCancelamento: ({ onCancelado }: { onCancelado: () => void }) => (
    <button onClick={onCancelado} data-testid="botao-cancelamento">
      Cancelar
    </button>
  )
}));

// Dados de teste
const mockAgendamento: AgendamentoCompleto = {
  agendamento_id: 1,
  cliente_user_id: 'cliente-1',
  empresa_id: 1,
  colaborador_user_id: 'colaborador-1',
  servico_id: 1,
  data_hora_inicio: '2025-01-27T10:00:00Z',
  data_hora_fim: '2025-01-27T11:00:00Z',
  observacoes_cliente: 'Teste de observação',
  status_agendamento: 'Pendente',
  forma_pagamento: 'Online',
  status_pagamento: 'Pendente',
  valor_total: 50.00,
  valor_desconto: 0,
  stripe_payment_intent_id: null,
  codigo_confirmacao: 'ABC123',
  prazo_confirmacao: '2025-01-27T09:00:00Z',
  created_at: '2025-01-26T10:00:00Z',
  updated_at: '2025-01-26T10:00:00Z',
  servico: {
    servico_id: 1,
    nome_servico: 'Corte de Cabelo',
    descricao: 'Corte masculino',
    duracao_minutos: 60,
    preco: 50.00,
    ativo: true,
    categoria: 'Cabelo',
    empresa_id: 1,
    created_at: '2025-01-26T10:00:00Z',
    updated_at: '2025-01-26T10:00:00Z'
  },
  cliente: {
    id: 'cliente-1',
    name: 'João Silva',
    email: '<EMAIL>'
  },
  colaborador: {
    id: 'colaborador-1',
    name: 'Maria Santos',
    email: '<EMAIL>'
  },
  empresa: {
    empresa_id: 1,
    nome_empresa: 'Barbearia do João',
    descricao: 'A melhor barbearia',
    endereco: 'Rua A, 123',
    telefone: '(11) 99999-9999',
    email: '<EMAIL>',
    ativo: true,
    proprietario_user_id: 'prop-1',
    created_at: '2025-01-26T10:00:00Z',
    updated_at: '2025-01-26T10:00:00Z'
  }
};

describe('CardAgendamento', () => {
  const defaultProps = {
    agendamento: mockAgendamento,
    mostrarAcoes: true,
    loading: false,
    userRole: 'Proprietario'
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('deve renderizar informações básicas do agendamento', () => {
    render(<CardAgendamento {...defaultProps} />);

    expect(screen.getByText('Corte de Cabelo')).toBeInTheDocument();
    expect(screen.getByText('R$ 50,00')).toBeInTheDocument();
    expect(screen.getByText('Online')).toBeInTheDocument();
    expect(screen.getByText('João Silva')).toBeInTheDocument();
    expect(screen.getByText('Maria Santos')).toBeInTheDocument();
  });

  it('deve mostrar status pendente corretamente', () => {
    render(<CardAgendamento {...defaultProps} />);

    expect(screen.getByText('⏳ Pendente')).toBeInTheDocument();
  });

  it('deve mostrar observações do cliente quando existirem', () => {
    render(<CardAgendamento {...defaultProps} />);

    expect(screen.getByText('Observações do cliente:')).toBeInTheDocument();
    expect(screen.getByText('Teste de observação')).toBeInTheDocument();
  });

  it('deve mostrar código de confirmação', () => {
    render(<CardAgendamento {...defaultProps} />);

    expect(screen.getByText('Código: ABC123')).toBeInTheDocument();
  });

  it('deve mostrar botão de confirmar para agendamento pendente', () => {
    render(<CardAgendamento {...defaultProps} />);

    expect(screen.getByText('✅ Confirmar')).toBeInTheDocument();
  });

  it('deve chamar onConfirmar quando botão confirmar é clicado', async () => {
    const onConfirmar = jest.fn();
    render(<CardAgendamento {...defaultProps} onConfirmar={onConfirmar} />);

    const botaoConfirmar = screen.getByText('✅ Confirmar');
    fireEvent.click(botaoConfirmar);

    expect(onConfirmar).toHaveBeenCalledWith(1);
  });

  it('deve mostrar botão de recusar para agendamento pendente', () => {
    render(<CardAgendamento {...defaultProps} />);

    expect(screen.getByText('❌ Recusar')).toBeInTheDocument();
  });

  it('deve chamar onRecusar quando botão recusar é clicado', async () => {
    const onRecusar = jest.fn();
    render(<CardAgendamento {...defaultProps} onRecusar={onRecusar} />);

    const botaoRecusar = screen.getByText('❌ Recusar');
    fireEvent.click(botaoRecusar);

    expect(onRecusar).toHaveBeenCalledWith(1);
  });

  it('deve mostrar botão de concluir para agendamento confirmado', () => {
    const agendamentoConfirmado = {
      ...mockAgendamento,
      status_agendamento: 'Confirmado' as const
    };

    render(<CardAgendamento {...defaultProps} agendamento={agendamentoConfirmado} />);

    expect(screen.getByText('✨ Concluir')).toBeInTheDocument();
  });

  it('deve mostrar botão marcar como pago para pagamento local pendente', () => {
    const agendamentoLocal = {
      ...mockAgendamento,
      forma_pagamento: 'Local' as const,
      status_pagamento: 'Pendente' as const
    };

    render(<CardAgendamento {...defaultProps} agendamento={agendamentoLocal} />);

    expect(screen.getByText('💰 Marcar como Pago')).toBeInTheDocument();
  });

  it('deve mostrar botão de ver detalhes quando callback é fornecido', () => {
    const onVerDetalhes = jest.fn();
    render(<CardAgendamento {...defaultProps} onVerDetalhes={onVerDetalhes} />);

    expect(screen.getByText('👁️ Ver Detalhes')).toBeInTheDocument();
  });

  it('deve chamar onVerDetalhes quando botão ver detalhes é clicado', async () => {
    const onVerDetalhes = jest.fn();
    render(<CardAgendamento {...defaultProps} onVerDetalhes={onVerDetalhes} />);

    const botaoDetalhes = screen.getByText('👁️ Ver Detalhes');
    fireEvent.click(botaoDetalhes);

    expect(onVerDetalhes).toHaveBeenCalledWith(mockAgendamento);
  });

  it('deve desabilitar botões quando loading é true', () => {
    render(<CardAgendamento {...defaultProps} loading={true} />);

    const botaoConfirmar = screen.getByText('✅ Confirmar');
    expect(botaoConfirmar).toBeDisabled();
  });

  it('deve não mostrar ações quando mostrarAcoes é false', () => {
    render(<CardAgendamento {...defaultProps} mostrarAcoes={false} />);

    expect(screen.queryByText('✅ Confirmar')).not.toBeInTheDocument();
    expect(screen.queryByText('❌ Recusar')).not.toBeInTheDocument();
  });

  it('deve mostrar informações diferentes para cliente', () => {
    render(<CardAgendamento {...defaultProps} userRole="Usuario" />);

    // Para clientes, deve mostrar empresa e colaborador
    expect(screen.getByText('Barbearia do João')).toBeInTheDocument();
    expect(screen.getByText('Maria Santos')).toBeInTheDocument();
    
    // Não deve mostrar nome do cliente
    expect(screen.queryByText('João Silva')).not.toBeInTheDocument();
  });

  it('deve mostrar status expirado para agendamentos com prazo vencido', () => {
    const agendamentoExpirado = {
      ...mockAgendamento,
      prazo_confirmacao: '2025-01-25T09:00:00Z' // Data no passado
    };

    render(<CardAgendamento {...defaultProps} agendamento={agendamentoExpirado} />);

    expect(screen.getByText('⏳ Expirado')).toBeInTheDocument();
  });

  it('deve aplicar classe de destaque para agendamentos próximos do prazo', () => {
    const agendamentoProximoPrazo = {
      ...mockAgendamento,
      prazo_confirmacao: new Date(Date.now() + 60 * 60 * 1000).toISOString() // 1 hora no futuro
    };

    const { container } = render(<CardAgendamento {...defaultProps} agendamento={agendamentoProximoPrazo} />);

    const card = container.querySelector('.ring-2.ring-orange-200');
    expect(card).toBeInTheDocument();
  });

  it('deve mostrar status de pagamento para pagamento local', () => {
    const agendamentoLocal = {
      ...mockAgendamento,
      forma_pagamento: 'Local' as const,
      status_pagamento: 'Pago' as const
    };

    render(<CardAgendamento {...defaultProps} agendamento={agendamentoLocal} />);

    expect(screen.getByText('✅ Pago')).toBeInTheDocument();
  });

  it('deve renderizar sem erros quando dados opcionais estão ausentes', () => {
    const agendamentoMinimo = {
      ...mockAgendamento,
      observacoes_cliente: null,
      cliente: null,
      colaborador: null,
      empresa: null
    };

    expect(() => {
      render(<CardAgendamento {...defaultProps} agendamento={agendamentoMinimo} />);
    }).not.toThrow();
  });
});
