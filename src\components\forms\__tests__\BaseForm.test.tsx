/**
 * Testes para o componente BaseForm
 */

import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { BaseForm, FormField } from '../BaseForm';

describe('BaseForm', () => {
  const mockOnSubmit = jest.fn();
  const mockOnCancel = jest.fn();

  const defaultFields: FormField[] = [
    {
      name: 'nome',
      label: 'Nome',
      type: 'text',
      required: true,
      placeholder: 'Digite seu nome'
    },
    {
      name: 'email',
      label: 'Email',
      type: 'email',
      required: true,
      validation: (value: string) => {
        if (!value.includes('@')) return 'Email inválido';
      }
    },
    {
      name: 'descricao',
      label: 'Descrição',
      type: 'textarea',
      required: false,
      helperText: 'Opcional'
    },
    {
      name: 'categoria',
      label: 'Categoria',
      type: 'select',
      required: true,
      options: [
        { value: 'cabelo', label: 'Cabelo' },
        { value: 'barba', label: 'Barba' }
      ]
    }
  ];

  beforeEach(() => {
    jest.clearAllMocks();
  });

  it('deve renderizar todos os campos corretamente', () => {
    render(
      <BaseForm
        title="Teste Form"
        fields={defaultFields}
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.getByText('Teste Form')).toBeInTheDocument();
    expect(screen.getByLabelText('Nome *')).toBeInTheDocument();
    expect(screen.getByLabelText('Email *')).toBeInTheDocument();
    expect(screen.getByLabelText('Descrição')).toBeInTheDocument();
    expect(screen.getByLabelText('Categoria *')).toBeInTheDocument();
  });

  it('deve mostrar placeholder nos campos', () => {
    render(
      <BaseForm
        title="Teste Form"
        fields={defaultFields}
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.getByPlaceholderText('Digite seu nome')).toBeInTheDocument();
  });

  it('deve mostrar helper text quando fornecido', () => {
    render(
      <BaseForm
        title="Teste Form"
        fields={defaultFields}
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.getByText('Opcional')).toBeInTheDocument();
  });

  it('deve renderizar opções do select', () => {
    render(
      <BaseForm
        title="Teste Form"
        fields={defaultFields}
        onSubmit={mockOnSubmit}
      />
    );

    const select = screen.getByLabelText('Categoria *');
    expect(select).toBeInTheDocument();
    
    // Verificar se as opções estão presentes
    expect(screen.getByText('Cabelo')).toBeInTheDocument();
    expect(screen.getByText('Barba')).toBeInTheDocument();
  });

  it('deve validar campos obrigatórios', async () => {
    render(
      <BaseForm
        title="Teste Form"
        fields={defaultFields}
        onSubmit={mockOnSubmit}
      />
    );

    const submitButton = screen.getByText('Salvar');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Nome é obrigatório')).toBeInTheDocument();
      expect(screen.getByText('Email é obrigatório')).toBeInTheDocument();
      expect(screen.getByText('Categoria é obrigatório')).toBeInTheDocument();
    });

    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('deve executar validação customizada', async () => {
    render(
      <BaseForm
        title="Teste Form"
        fields={defaultFields}
        onSubmit={mockOnSubmit}
      />
    );

    const emailInput = screen.getByLabelText('Email *');
    fireEvent.change(emailInput, { target: { value: 'email-invalido' } });

    const submitButton = screen.getByText('Salvar');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(screen.getByText('Email inválido')).toBeInTheDocument();
    });

    expect(mockOnSubmit).not.toHaveBeenCalled();
  });

  it('deve limpar erro quando usuário começa a digitar', async () => {
    render(
      <BaseForm
        title="Teste Form"
        fields={defaultFields}
        onSubmit={mockOnSubmit}
      />
    );

    const nomeInput = screen.getByLabelText('Nome *');
    const submitButton = screen.getByText('Salvar');

    // Primeiro, gerar erro
    fireEvent.click(submitButton);
    await waitFor(() => {
      expect(screen.getByText('Nome é obrigatório')).toBeInTheDocument();
    });

    // Depois, digitar para limpar erro
    fireEvent.change(nomeInput, { target: { value: 'João' } });
    
    await waitFor(() => {
      expect(screen.queryByText('Nome é obrigatório')).not.toBeInTheDocument();
    });
  });

  it('deve submeter formulário com dados válidos', async () => {
    render(
      <BaseForm
        title="Teste Form"
        fields={defaultFields}
        onSubmit={mockOnSubmit}
      />
    );

    // Preencher campos
    fireEvent.change(screen.getByLabelText('Nome *'), { target: { value: 'João Silva' } });
    fireEvent.change(screen.getByLabelText('Email *'), { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByLabelText('Descrição'), { target: { value: 'Descrição teste' } });
    fireEvent.change(screen.getByLabelText('Categoria *'), { target: { value: 'cabelo' } });

    const submitButton = screen.getByText('Salvar');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmit).toHaveBeenCalledWith({
        nome: 'João Silva',
        email: '<EMAIL>',
        descricao: 'Descrição teste',
        categoria: 'cabelo'
      });
    });
  });

  it('deve chamar onCancel quando botão cancelar é clicado', () => {
    render(
      <BaseForm
        title="Teste Form"
        fields={defaultFields}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
      />
    );

    const cancelButton = screen.getByText('Cancelar');
    fireEvent.click(cancelButton);

    expect(mockOnCancel).toHaveBeenCalled();
  });

  it('deve não mostrar botão cancelar quando onCancel não é fornecido', () => {
    render(
      <BaseForm
        title="Teste Form"
        fields={defaultFields}
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.queryByText('Cancelar')).not.toBeInTheDocument();
  });

  it('deve usar dados iniciais quando fornecidos', () => {
    const initialData = {
      nome: 'João',
      email: '<EMAIL>'
    };

    render(
      <BaseForm
        title="Teste Form"
        fields={defaultFields}
        onSubmit={mockOnSubmit}
        initialData={initialData}
      />
    );

    expect(screen.getByDisplayValue('João')).toBeInTheDocument();
    expect(screen.getByDisplayValue('<EMAIL>')).toBeInTheDocument();
  });

  it('deve desabilitar campos quando loading é true', () => {
    render(
      <BaseForm
        title="Teste Form"
        fields={defaultFields}
        onSubmit={mockOnSubmit}
        loading={true}
      />
    );

    const nomeInput = screen.getByLabelText('Nome *');
    const submitButton = screen.getByText('Salvar');

    expect(nomeInput).toBeDisabled();
    expect(submitButton).toBeDisabled();
  });

  it('deve usar textos customizados para botões', () => {
    render(
      <BaseForm
        title="Teste Form"
        fields={defaultFields}
        onSubmit={mockOnSubmit}
        onCancel={mockOnCancel}
        submitText="Criar"
        cancelText="Voltar"
      />
    );

    expect(screen.getByText('Criar')).toBeInTheDocument();
    expect(screen.getByText('Voltar')).toBeInTheDocument();
  });

  it('deve mostrar descrição quando fornecida', () => {
    render(
      <BaseForm
        title="Teste Form"
        description="Preencha os campos abaixo"
        fields={defaultFields}
        onSubmit={mockOnSubmit}
      />
    );

    expect(screen.getByText('Preencha os campos abaixo')).toBeInTheDocument();
  });

  it('deve tratar erro durante submissão', async () => {
    const mockOnSubmitError = jest.fn().mockRejectedValue(new Error('Erro de teste'));

    render(
      <BaseForm
        title="Teste Form"
        fields={defaultFields}
        onSubmit={mockOnSubmitError}
      />
    );

    // Preencher campos válidos
    fireEvent.change(screen.getByLabelText('Nome *'), { target: { value: 'João' } });
    fireEvent.change(screen.getByLabelText('Email *'), { target: { value: '<EMAIL>' } });
    fireEvent.change(screen.getByLabelText('Categoria *'), { target: { value: 'cabelo' } });

    const submitButton = screen.getByText('Salvar');
    fireEvent.click(submitButton);

    await waitFor(() => {
      expect(mockOnSubmitError).toHaveBeenCalled();
    });

    // Verificar se o botão volta ao estado normal após erro
    await waitFor(() => {
      expect(submitButton).not.toBeDisabled();
    });
  });
});
