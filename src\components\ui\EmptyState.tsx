'use client';

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { MagicCard } from './magic-card';
import { TextAnimate } from './text-animate';
import { ShimmerButton } from './shimmer-button';

interface EmptyStateProps {
  icon?: string;
  title: string;
  description: string;
  actionText?: string;
  actionHref?: string;
  onAction?: () => void;
  className?: string;
  gradientColor?: string;
  suggestions?: string[];
}

export function EmptyState({
  icon = '📊',
  title,
  description,
  actionText,
  actionHref,
  onAction,
  className = '',
  gradientColor = '#6B7280',
  suggestions = []
}: EmptyStateProps) {
  const renderAction = () => {
    if (!actionText) return null;

    const buttonContent = (
      <ShimmerButton 
        className="bg-blue-600 hover:bg-blue-700 text-white"
        shimmerColor="#60A5FA"
        onClick={onAction}
      >
        {actionText}
      </ShimmerButton>
    );

    if (actionHref) {
      return <Link href={actionHref}>{buttonContent}</Link>;
    }

    return buttonContent;
  };

  return (
    <MagicCard className={`p-8 text-center ${className}`} gradientColor={gradientColor}>
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="space-y-6"
      >
        {/* Ícone */}
        <motion.div 
          className="text-6xl"
          animate={{ scale: [1, 1.05, 1] }}
          transition={{ duration: 3, repeat: Infinity }}
        >
          {icon}
        </motion.div>

        {/* Título */}
        <TextAnimate
          animation="blurInUp"
          className="text-xl font-semibold text-gray-700 dark:text-gray-300"
        >
          {title}
        </TextAnimate>

        {/* Descrição */}
        <p className="text-gray-500 dark:text-gray-400 max-w-md mx-auto">
          {description}
        </p>

        {/* Sugestões */}
        {suggestions.length > 0 && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.3 }}
            className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 max-w-md mx-auto"
          >
            <h4 className="font-medium text-blue-800 dark:text-blue-200 mb-2">
              💡 Sugestões:
            </h4>
            <ul className="text-sm text-blue-700 dark:text-blue-300 space-y-1">
              {suggestions.map((suggestion, index) => (
                <li key={index} className="flex items-start space-x-2">
                  <span className="text-blue-500 mt-0.5">•</span>
                  <span>{suggestion}</span>
                </li>
              ))}
            </ul>
          </motion.div>
        )}

        {/* Ação */}
        {renderAction() && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            {renderAction()}
          </motion.div>
        )}
      </motion.div>
    </MagicCard>
  );
}

// Componentes pré-configurados para casos comuns
export function EmptyMetrics({ className = '' }: { className?: string }) {
  return (
    <EmptyState
      icon="📈"
      title="Nenhuma métrica disponível"
      description="Comece criando agendamentos para ver suas métricas de negócio aqui."
      actionText="Criar Primeiro Agendamento"
      actionHref="/proprietario/agendamentos/criar"
      className={className}
      gradientColor="#3B82F6"
      suggestions={[
        "Cadastre seus serviços primeiro",
        "Configure seus horários de funcionamento",
        "Divulgue sua empresa para clientes"
      ]}
    />
  );
}

export function EmptyServices({ className = '' }: { className?: string }) {
  return (
    <EmptyState
      icon="⚙️"
      title="Nenhum serviço cadastrado"
      description="Cadastre os serviços que sua empresa oferece para começar a receber agendamentos."
      actionText="Cadastrar Primeiro Serviço"
      actionHref="/proprietario/servicos/criar"
      className={className}
      gradientColor="#10B981"
      suggestions={[
        "Defina nome, descrição e preço do serviço",
        "Configure a duração estimada",
        "Adicione categorias para organizar"
      ]}
    />
  );
}

export function EmptyAppointments({ className = '' }: { className?: string }) {
  return (
    <EmptyState
      icon="📅"
      title="Nenhum agendamento encontrado"
      description="Quando clientes agendarem serviços, eles aparecerão aqui."
      actionText="Ver Todos os Agendamentos"
      actionHref="/proprietario/agendamentos"
      className={className}
      gradientColor="#F59E0B"
      suggestions={[
        "Compartilhe o link da sua empresa",
        "Configure notificações para novos agendamentos",
        "Verifique se seus serviços estão ativos"
      ]}
    />
  );
}

export function EmptyClients({ className = '' }: { className?: string }) {
  return (
    <EmptyState
      icon="👥"
      title="Nenhum cliente cadastrado"
      description="Seus clientes aparecerão aqui conforme fizerem agendamentos."
      actionText="Ver Estratégias de Marketing"
      actionHref="/proprietario/marketing"
      className={className}
      gradientColor="#8B5CF6"
      suggestions={[
        "Divulgue sua empresa nas redes sociais",
        "Configure promoções especiais",
        "Peça indicações para clientes atuais"
      ]}
    />
  );
}

export function EmptyReports({ className = '' }: { className?: string }) {
  return (
    <EmptyState
      icon="📊"
      title="Relatórios indisponíveis"
      description="Relatórios serão gerados automaticamente conforme você tiver dados de agendamentos."
      actionText="Ver Métricas Básicas"
      actionHref="/proprietario/dashboard"
      className={className}
      gradientColor="#EF4444"
      suggestions={[
        "Aguarde alguns agendamentos serem criados",
        "Verifique se os dados estão sendo coletados",
        "Configure integrações de pagamento"
      ]}
    />
  );
}
