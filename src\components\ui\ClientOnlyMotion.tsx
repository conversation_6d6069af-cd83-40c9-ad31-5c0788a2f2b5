'use client';

import React, { useState, useEffect } from 'react';
import { motion, MotionProps } from 'framer-motion';

interface ClientOnlyMotionProps extends MotionProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  as?: keyof typeof motion;
}

export function ClientOnlyMotion({ 
  children, 
  fallback = null, 
  as = 'div',
  ...motionProps 
}: ClientOnlyMotionProps) {
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  if (!isHydrated) {
    // Durante SSR e antes da hidratação, renderizar versão estática
    if (fallback) {
      return <>{fallback}</>;
    }
    
    // Renderizar versão estática sem animação
    const Component = as as keyof JSX.IntrinsicElements;
    const { initial, animate, exit, transition, variants, ...staticProps } = motionProps;
    
    return React.createElement(Component, staticProps, children);
  }

  // Após hidratação, renderizar com motion
  const MotionComponent = motion[as];
  return React.createElement(MotionComponent, motionProps, children);
}

// Componentes específicos para casos comuns
export function ClientOnlyMotionDiv(props: Omit<ClientOnlyMotionProps, 'as'>) {
  return <ClientOnlyMotion as="div" {...props} />;
}

export function ClientOnlyMotionP(props: Omit<ClientOnlyMotionProps, 'as'>) {
  return <ClientOnlyMotion as="p" {...props} />;
}

export function ClientOnlyMotionH1(props: Omit<ClientOnlyMotionProps, 'as'>) {
  return <ClientOnlyMotion as="h1" {...props} />;
}

export function ClientOnlyMotionH2(props: Omit<ClientOnlyMotionProps, 'as'>) {
  return <ClientOnlyMotion as="h2" {...props} />;
}

export function ClientOnlyMotionH3(props: Omit<ClientOnlyMotionProps, 'as'>) {
  return <ClientOnlyMotion as="h3" {...props} />;
}
