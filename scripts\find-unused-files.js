#!/usr/bin/env node

/**
 * Script para identificar arquivos não utilizados
 * Analisa imports e referências para encontrar arquivos órfãos
 */

const fs = require('fs');
const path = require('path');

const CONFIG = {
  srcDir: path.join(__dirname, '../src'),
  excludeDirs: ['node_modules', '.next', 'dist', 'build', '__tests__'],
  fileExtensions: ['.ts', '.tsx', '.js', '.jsx'],
  entryPoints: [
    'src/app/layout.tsx',
    'src/app/page.tsx',
    'src/app/**/page.tsx',
    'src/app/**/layout.tsx',
    'src/app/api/**/route.ts'
  ]
};

// Arquivos que devem ser mantidos mesmo se não referenciados diretamente
const KEEP_FILES = [
  'src/app/globals.css',
  'src/app/favicon.ico',
  'src/middleware.ts',
  'src/instrumentation.ts',
  'next.config.ts',
  'tailwind.config.ts',
  'jest.config.js',
  'jest.setup.js'
];

function readFilesRecursively(dir) {
  const files = [];
  
  function traverse(currentDir) {
    const items = fs.readdirSync(currentDir);
    
    for (const item of items) {
      const fullPath = path.join(currentDir, item);
      const stat = fs.statSync(fullPath);
      
      if (stat.isDirectory()) {
        const dirName = path.basename(fullPath);
        if (!CONFIG.excludeDirs.includes(dirName) && !dirName.startsWith('.')) {
          traverse(fullPath);
        }
      } else if (stat.isFile()) {
        const ext = path.extname(fullPath);
        if (CONFIG.fileExtensions.includes(ext)) {
          files.push(fullPath);
        }
      }
    }
  }
  
  traverse(dir);
  return files;
}

function extractImports(content) {
  const imports = new Set();
  
  // Padrões de import
  const patterns = [
    // import ... from '...'
    /import\s+.*?\s+from\s+['"]([^'"]+)['"]/g,
    // import('...')
    /import\s*\(\s*['"]([^'"]+)['"]\s*\)/g,
    // require('...')
    /require\s*\(\s*['"]([^'"]+)['"]\s*\)/g,
    // dynamic import
    /await\s+import\s*\(\s*['"]([^'"]+)['"]\s*\)/g
  ];
  
  for (const pattern of patterns) {
    let match;
    while ((match = pattern.exec(content)) !== null) {
      const importPath = match[1];
      if (importPath.startsWith('.') || importPath.startsWith('@/')) {
        imports.add(importPath);
      }
    }
  }
  
  return Array.from(imports);
}

function resolveImportPath(importPath, fromFile) {
  const fromDir = path.dirname(fromFile);
  
  // Resolver @/ para src/
  if (importPath.startsWith('@/')) {
    importPath = importPath.replace('@/', 'src/');
    return path.resolve(path.join(__dirname, '..'), importPath);
  }
  
  // Resolver caminhos relativos
  if (importPath.startsWith('.')) {
    return path.resolve(fromDir, importPath);
  }
  
  return null;
}

function findPossibleFiles(basePath) {
  const possibleFiles = [];
  const extensions = ['.ts', '.tsx', '.js', '.jsx'];
  
  // Tentar com extensões
  for (const ext of extensions) {
    const filePath = basePath + ext;
    if (fs.existsSync(filePath)) {
      possibleFiles.push(filePath);
    }
  }
  
  // Tentar como diretório com index
  for (const ext of extensions) {
    const indexPath = path.join(basePath, 'index' + ext);
    if (fs.existsSync(indexPath)) {
      possibleFiles.push(indexPath);
    }
  }
  
  return possibleFiles;
}

function analyzeFileUsage() {
  console.log('🔍 Analisando uso de arquivos...\n');
  
  const allFiles = readFilesRecursively(CONFIG.srcDir);
  const referencedFiles = new Set();
  const importGraph = new Map();
  
  console.log(`📊 Encontrados ${allFiles.length} arquivos para análise\n`);
  
  // Construir grafo de dependências
  for (const file of allFiles) {
    try {
      const content = fs.readFileSync(file, 'utf8');
      const imports = extractImports(content);
      
      importGraph.set(file, imports);
      
      // Resolver imports para arquivos reais
      for (const importPath of imports) {
        const resolvedPath = resolveImportPath(importPath, file);
        if (resolvedPath) {
          const possibleFiles = findPossibleFiles(resolvedPath);
          for (const possibleFile of possibleFiles) {
            referencedFiles.add(possibleFile);
          }
        }
      }
    } catch (error) {
      console.error(`❌ Erro ao analisar ${file}:`, error.message);
    }
  }
  
  // Adicionar entry points como referenciados
  const entryPoints = allFiles.filter(file => {
    const relativePath = path.relative(path.join(__dirname, '..'), file);
    return (
      relativePath.includes('page.tsx') ||
      relativePath.includes('layout.tsx') ||
      relativePath.includes('route.ts') ||
      relativePath.includes('middleware.ts') ||
      KEEP_FILES.some(keepFile => relativePath.includes(keepFile))
    );
  });
  
  for (const entryPoint of entryPoints) {
    referencedFiles.add(entryPoint);
  }
  
  // Encontrar arquivos não referenciados
  const unusedFiles = allFiles.filter(file => !referencedFiles.has(file));
  
  // Filtrar arquivos que devem ser mantidos
  const safeToRemove = unusedFiles.filter(file => {
    const relativePath = path.relative(path.join(__dirname, '..'), file);
    return !KEEP_FILES.some(keepFile => relativePath.includes(keepFile));
  });
  
  return {
    totalFiles: allFiles.length,
    referencedFiles: referencedFiles.size,
    unusedFiles: unusedFiles.length,
    safeToRemove: safeToRemove,
    importGraph
  };
}

function categorizeUnusedFiles(unusedFiles) {
  const categories = {
    tests: [],
    components: [],
    hooks: [],
    utils: [],
    types: [],
    pages: [],
    api: [],
    other: []
  };
  
  for (const file of unusedFiles) {
    const relativePath = path.relative(path.join(__dirname, '..'), file);
    
    if (relativePath.includes('__tests__') || relativePath.includes('.test.') || relativePath.includes('.spec.')) {
      categories.tests.push(relativePath);
    } else if (relativePath.includes('components/')) {
      categories.components.push(relativePath);
    } else if (relativePath.includes('hooks/')) {
      categories.hooks.push(relativePath);
    } else if (relativePath.includes('utils/')) {
      categories.utils.push(relativePath);
    } else if (relativePath.includes('types/')) {
      categories.types.push(relativePath);
    } else if (relativePath.includes('app/') && relativePath.includes('page.tsx')) {
      categories.pages.push(relativePath);
    } else if (relativePath.includes('api/')) {
      categories.api.push(relativePath);
    } else {
      categories.other.push(relativePath);
    }
  }
  
  return categories;
}

function generateReport(analysis) {
  console.log('📋 RELATÓRIO DE ARQUIVOS NÃO UTILIZADOS');
  console.log('=====================================\n');
  
  console.log(`📊 Estatísticas:`);
  console.log(`   • Total de arquivos: ${analysis.totalFiles}`);
  console.log(`   • Arquivos referenciados: ${analysis.referencedFiles}`);
  console.log(`   • Arquivos não utilizados: ${analysis.unusedFiles}`);
  console.log(`   • Seguros para remoção: ${analysis.safeToRemove.length}\n`);
  
  if (analysis.safeToRemove.length === 0) {
    console.log('✅ Nenhum arquivo não utilizado encontrado!\n');
    return;
  }
  
  const categories = categorizeUnusedFiles(analysis.safeToRemove);
  
  for (const [category, files] of Object.entries(categories)) {
    if (files.length > 0) {
      console.log(`🗂️  ${category.toUpperCase()} (${files.length} arquivos):`);
      for (const file of files) {
        console.log(`   • ${file}`);
      }
      console.log('');
    }
  }
  
  console.log('⚠️  ATENÇÃO: Verifique manualmente antes de remover!');
  console.log('   • Alguns arquivos podem ser usados dinamicamente');
  console.log('   • Arquivos de configuração podem ser necessários');
  console.log('   • Testes podem ser importantes para CI/CD\n');
}

function main() {
  console.log('🔍 Iniciando análise de arquivos não utilizados...\n');
  
  const analysis = analyzeFileUsage();
  generateReport(analysis);
  
  console.log('✨ Análise concluída!\n');
}

if (require.main === module) {
  main();
}

module.exports = { analyzeFileUsage, categorizeUnusedFiles };
