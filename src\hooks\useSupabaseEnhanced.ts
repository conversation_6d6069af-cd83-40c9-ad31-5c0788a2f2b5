'use client';

import { useState, useEffect, useCallback, useRef } from 'react';
import { getEnhancedSupabaseClient, EnhancedClientConfig } from '@/lib/supabase/enhanced-client';
import { useAuth } from '@/contexts/AuthContext';

// Tipos para configurações de query
interface QueryOptions {
  select?: string;
  filters?: Record<string, any>;
  orderBy?: Array<{ column: string; ascending?: boolean }>;
  limit?: number;
  offset?: number;
  cache?: boolean;
  cacheTTL?: number;
  enabled?: boolean;
  refetchOnWindowFocus?: boolean;
  refetchInterval?: number;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
}

interface QueryResult<T> {
  data: T[] | null;
  loading: boolean;
  error: string | null;
  fromCache: boolean;
  refetch: () => Promise<void>;
  invalidate: () => void;
}

interface MutationOptions {
  returning?: string;
  onConflict?: string;
  ignoreDuplicates?: boolean;
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  invalidateQueries?: string[];
}

interface MutationResult {
  mutate: (data: any) => Promise<any>;
  loading: boolean;
  error: string | null;
  data: any;
  reset: () => void;
}

// Hook principal para queries
export function useSupabaseQuery<T = any>(
  table: string,
  options: QueryOptions = {}
): QueryResult<T> {
  const {
    enabled = true,
    refetchOnWindowFocus = false,
    refetchInterval,
    onSuccess,
    onError,
    ...queryOptions
  } = options;

  const [state, setState] = useState<{
    data: T[] | null;
    loading: boolean;
    error: string | null;
    fromCache: boolean;
  }>({
    data: null,
    loading: false,
    error: null,
    fromCache: false
  });

  const client = getEnhancedSupabaseClient();
  const abortControllerRef = useRef<AbortController | null>(null);
  const intervalRef = useRef<NodeJS.Timeout | null>(null);

  const fetchData = useCallback(async () => {
    if (!enabled) return;

    // Cancelar request anterior se existir
    if (abortControllerRef.current) {
      abortControllerRef.current.abort();
    }

    abortControllerRef.current = new AbortController();

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await client.query<T>(table, queryOptions);

      if (result.error) {
        throw new Error(result.error.message || 'Erro na consulta');
      }

      setState({
        data: result.data,
        loading: false,
        error: null,
        fromCache: result.fromCache
      });

      if (onSuccess && result.data) {
        onSuccess(result.data);
      }
    } catch (error: any) {
      if (error.name !== 'AbortError') {
        const errorMessage = error.message || 'Erro desconhecido';
        setState(prev => ({
          ...prev,
          loading: false,
          error: errorMessage
        }));

        if (onError) {
          onError(error);
        }
      }
    }
  }, [enabled, table, JSON.stringify(queryOptions), onSuccess, onError, client]);

  const invalidate = useCallback(() => {
    client.clearCache(table);
  }, [client, table]);

  // Fetch inicial
  useEffect(() => {
    fetchData();

    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort();
      }
    };
  }, [fetchData]);

  // Refetch em intervalo
  useEffect(() => {
    if (refetchInterval && enabled) {
      intervalRef.current = setInterval(fetchData, refetchInterval);
      
      return () => {
        if (intervalRef.current) {
          clearInterval(intervalRef.current);
        }
      };
    }
  }, [refetchInterval, enabled, fetchData]);

  // Refetch no foco da janela
  useEffect(() => {
    if (refetchOnWindowFocus && enabled) {
      const handleFocus = () => fetchData();
      window.addEventListener('focus', handleFocus);
      
      return () => {
        window.removeEventListener('focus', handleFocus);
      };
    }
  }, [refetchOnWindowFocus, enabled, fetchData]);

  return {
    ...state,
    refetch: fetchData,
    invalidate
  };
}

// Hook para mutações
export function useSupabaseMutation(
  table: string,
  operation: 'insert' | 'update' | 'delete' | 'upsert',
  options: MutationOptions = {}
): MutationResult {
  const {
    onSuccess,
    onError,
    invalidateQueries = [],
    ...mutationOptions
  } = options;

  const [state, setState] = useState({
    loading: false,
    error: null as string | null,
    data: null as any
  });

  const client = getEnhancedSupabaseClient();

  const mutate = useCallback(async (data: any) => {
    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await client.mutate(table, operation, data, mutationOptions);

      if (result.error) {
        throw new Error(result.error.message || 'Erro na mutação');
      }

      setState({
        loading: false,
        error: null,
        data: result.data
      });

      // Invalidar queries relacionadas
      invalidateQueries.forEach(queryTable => {
        client.clearCache(queryTable);
      });

      if (onSuccess && result.data) {
        onSuccess(result.data);
      }

      return result.data;
    } catch (error: any) {
      const errorMessage = error.message || 'Erro desconhecido';
      setState(prev => ({
        ...prev,
        loading: false,
        error: errorMessage
      }));

      if (onError) {
        onError(error);
      }

      throw error;
    }
  }, [table, operation, JSON.stringify(mutationOptions), onSuccess, onError, invalidateQueries, client]);

  const reset = useCallback(() => {
    setState({
      loading: false,
      error: null,
      data: null
    });
  }, []);

  return {
    ...state,
    mutate,
    reset
  };
}

// Hook para subscription em tempo real
export function useSupabaseSubscription(
  table: string,
  options: {
    event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
    filter?: string;
    schema?: string;
    onData?: (payload: any) => void;
    onError?: (error: Error) => void;
    enabled?: boolean;
  } = {}
) {
  const {
    event = '*',
    filter,
    schema = 'public',
    onData,
    onError,
    enabled = true
  } = options;

  const client = getEnhancedSupabaseClient();
  const channelRef = useRef<any>(null);

  useEffect(() => {
    if (!enabled || !onData) return;

    try {
      const channel = client.subscribe(table, { event, filter, schema });
      
      channel.on('postgres_changes', { event, schema, table, filter }, (payload) => {
        onData(payload);
      });

      channel.subscribe((status: string) => {
        if (status === 'SUBSCRIBED') {
          console.log(`[Supabase] Subscribed to ${table} changes`);
        } else if (status === 'CHANNEL_ERROR') {
          const error = new Error(`Subscription error for table ${table}`);
          console.error('[Supabase] Subscription error:', error);
          if (onError) {
            onError(error);
          }
        }
      });

      channelRef.current = channel;

      return () => {
        if (channelRef.current) {
          client.getClient().removeChannel(channelRef.current);
        }
      };
    } catch (error) {
      console.error('[Supabase] Error setting up subscription:', error);
      if (onError) {
        onError(error instanceof Error ? error : new Error('Subscription setup failed'));
      }
    }
  }, [table, event, filter, schema, onData, onError, enabled, client]);

  return {
    unsubscribe: () => {
      if (channelRef.current) {
        client.getClient().removeChannel(channelRef.current);
        channelRef.current = null;
      }
    }
  };
}

// Hook para paginação
export function useSupabasePagination<T = any>(
  table: string,
  options: QueryOptions & {
    pageSize?: number;
    initialPage?: number;
  } = {}
) {
  const { pageSize = 20, initialPage = 1, ...queryOptions } = options;
  const [currentPage, setCurrentPage] = useState(initialPage);
  const [totalCount, setTotalCount] = useState(0);

  // Query para dados da página atual
  const {
    data,
    loading,
    error,
    fromCache,
    refetch
  } = useSupabaseQuery<T>(table, {
    ...queryOptions,
    limit: pageSize,
    offset: (currentPage - 1) * pageSize
  });

  // Query para contar total de registros
  const { data: countData } = useSupabaseQuery(table, {
    select: 'count(*)',
    filters: queryOptions.filters,
    cache: true,
    cacheTTL: 10 * 60 * 1000 // 10 minutos
  });

  useEffect(() => {
    if (countData && countData[0]) {
      setTotalCount(countData[0].count || 0);
    }
  }, [countData]);

  const totalPages = Math.ceil(totalCount / pageSize);
  const hasNextPage = currentPage < totalPages;
  const hasPreviousPage = currentPage > 1;

  const goToPage = useCallback((page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  }, [totalPages]);

  const nextPage = useCallback(() => {
    if (hasNextPage) {
      setCurrentPage(prev => prev + 1);
    }
  }, [hasNextPage]);

  const previousPage = useCallback(() => {
    if (hasPreviousPage) {
      setCurrentPage(prev => prev - 1);
    }
  }, [hasPreviousPage]);

  return {
    data,
    loading,
    error,
    fromCache,
    refetch,
    currentPage,
    totalPages,
    totalCount,
    pageSize,
    hasNextPage,
    hasPreviousPage,
    goToPage,
    nextPage,
    previousPage
  };
}

// Hook para métricas do Supabase
export function useSupabaseMetrics() {
  const [metrics, setMetrics] = useState(null);
  const client = getEnhancedSupabaseClient();

  useEffect(() => {
    const updateMetrics = () => {
      setMetrics(client.getMetrics());
    };

    updateMetrics();
    const interval = setInterval(updateMetrics, 5000); // Atualizar a cada 5 segundos

    return () => clearInterval(interval);
  }, [client]);

  return metrics;
}

// Hook para configuração do cliente
export function useSupabaseConfig(config: EnhancedClientConfig) {
  const client = getEnhancedSupabaseClient(config);
  return client;
}
