"use client";

import { useEffect, useState } from 'react';

// Hook para detectar tamanho da tela
export function useScreenSize() {
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 1024,
    height: typeof window !== 'undefined' ? window.innerHeight : 768,
    isMobile: false,
    isTablet: false,
    isDesktop: false,
  });

  useEffect(() => {
    function updateScreenSize() {
      const width = window.innerWidth;
      const height = window.innerHeight;
      
      setScreenSize({
        width,
        height,
        isMobile: width < 768,
        isTablet: width >= 768 && width < 1024,
        isDesktop: width >= 1024,
      });
    }

    updateScreenSize();
    window.addEventListener('resize', updateScreenSize);
    
    return () => window.removeEventListener('resize', updateScreenSize);
  }, []);

  return screenSize;
}

// Hook para detectar preferências de movimento reduzido
export function useReducedMotion() {
  const [prefersReducedMotion, setPrefersReducedMotion] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-reduced-motion: reduce)');
    setPrefersReducedMotion(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setPrefersReducedMotion(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return prefersReducedMotion;
}

// Hook para detectar tema escuro
export function useDarkMode() {
  const [isDark, setIsDark] = useState(false);

  useEffect(() => {
    const mediaQuery = window.matchMedia('(prefers-color-scheme: dark)');
    setIsDark(mediaQuery.matches);

    const handleChange = (event: MediaQueryListEvent) => {
      setIsDark(event.matches);
    };

    mediaQuery.addEventListener('change', handleChange);
    return () => mediaQuery.removeEventListener('change', handleChange);
  }, []);

  return isDark;
}

// Configurações responsivas para componentes
export const responsiveConfig = {
  // Configurações do BentoGrid
  bentoGrid: {
    mobile: {
      columns: 1,
      gap: 4,
      autoRows: 'minmax(180px, auto)',
    },
    tablet: {
      columns: 2,
      gap: 5,
      autoRows: 'minmax(200px, auto)',
    },
    desktop: {
      columns: 6,
      gap: 6,
      autoRows: 'minmax(220px, auto)',
    },
  },

  // Configurações de partículas
  particles: {
    mobile: {
      quantity: 15,
      size: 0.6,
      staticity: 40,
      ease: 60,
    },
    tablet: {
      quantity: 25,
      size: 0.7,
      staticity: 35,
      ease: 70,
    },
    desktop: {
      quantity: 50,
      size: 0.8,
      staticity: 30,
      ease: 80,
    },
  },

  // Configurações de animação
  animation: {
    mobile: {
      duration: 0.4,
      stagger: 0.1,
      reducedMotion: {
        duration: 0.1,
        stagger: 0,
      },
    },
    tablet: {
      duration: 0.5,
      stagger: 0.08,
      reducedMotion: {
        duration: 0.2,
        stagger: 0,
      },
    },
    desktop: {
      duration: 0.6,
      stagger: 0.05,
      reducedMotion: {
        duration: 0.3,
        stagger: 0,
      },
    },
  },

  // Configurações de layout
  layout: {
    mobile: {
      padding: 4,
      headerSize: 'text-2xl',
      cardPadding: 4,
    },
    tablet: {
      padding: 5,
      headerSize: 'text-3xl',
      cardPadding: 5,
    },
    desktop: {
      padding: 6,
      headerSize: 'text-4xl',
      cardPadding: 6,
    },
  },
};

// Hook para obter configurações responsivas
export function useResponsiveConfig() {
  const { isMobile, isTablet, isDesktop } = useScreenSize();
  const prefersReducedMotion = useReducedMotion();

  const getConfig = (configType: keyof typeof responsiveConfig) => {
    const config = responsiveConfig[configType];
    
    if (isMobile) {
      const mobileConfig = config.mobile;
      if (prefersReducedMotion && 'reducedMotion' in mobileConfig) {
        return { ...mobileConfig, ...mobileConfig.reducedMotion };
      }
      return mobileConfig;
    }
    
    if (isTablet) {
      const tabletConfig = config.tablet;
      if (prefersReducedMotion && 'reducedMotion' in tabletConfig) {
        return { ...tabletConfig, ...tabletConfig.reducedMotion };
      }
      return tabletConfig;
    }
    
    const desktopConfig = config.desktop;
    if (prefersReducedMotion && 'reducedMotion' in desktopConfig) {
      return { ...desktopConfig, ...desktopConfig.reducedMotion };
    }
    return desktopConfig;
  };

  return {
    isMobile,
    isTablet,
    isDesktop,
    prefersReducedMotion,
    bentoGrid: getConfig('bentoGrid'),
    particles: getConfig('particles'),
    animation: getConfig('animation'),
    layout: getConfig('layout'),
  };
}

// Componente para otimização de performance
export function PerformanceOptimizer({ children }: { children: React.ReactNode }) {
  const { prefersReducedMotion } = useResponsiveConfig();

  useEffect(() => {
    // Reduzir animações se o usuário preferir
    if (prefersReducedMotion) {
      document.documentElement.style.setProperty('--animation-duration', '0.1s');
      document.documentElement.style.setProperty('--transition-duration', '0.1s');
    } else {
      document.documentElement.style.setProperty('--animation-duration', '0.6s');
      document.documentElement.style.setProperty('--transition-duration', '0.3s');
    }

    // Otimizar performance em dispositivos móveis
    if (window.innerWidth < 768) {
      // Reduzir qualidade de efeitos em dispositivos móveis
      document.documentElement.style.setProperty('--blur-amount', '2px');
      document.documentElement.style.setProperty('--shadow-intensity', '0.1');
    } else {
      document.documentElement.style.setProperty('--blur-amount', '10px');
      document.documentElement.style.setProperty('--shadow-intensity', '0.3');
    }
  }, [prefersReducedMotion]);

  return <>{children}</>;
}
