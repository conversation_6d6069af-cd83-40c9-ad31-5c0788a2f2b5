'use client';

import { useEffect, useRef } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { createClient } from '@/utils/supabase/client';

/**
 * Hook para recuperação automática de autenticação
 * Monitora e tenta recuperar sessões perdidas
 */
export function useAuthRecovery() {
  const { user, session, loading, initialized, refreshUser } = useAuth();
  const supabase = createClient();
  const recoveryAttempts = useRef(0);
  const maxRecoveryAttempts = 3;
  const recoveryTimeout = useRef<NodeJS.Timeout | null>(null);

  useEffect(() => {
    // Só tentar recuperação se estiver inicializado e não carregando
    if (!initialized || loading) return;

    // Se há usuário mas não há sessão, tentar recuperar
    if (user && !session && recoveryAttempts.current < maxRecoveryAttempts) {
      console.warn('Usuário autenticado mas sessão perdida. Tentando recuperar...');
      attemptRecovery();
    }

    // Se não há usuário nem sessão, resetar tentativas
    if (!user && !session) {
      recoveryAttempts.current = 0;
    }
  }, [user, session, initialized, loading]);

  const attemptRecovery = async () => {
    if (recoveryAttempts.current >= maxRecoveryAttempts) {
      console.error('Máximo de tentativas de recuperação atingido');
      return;
    }

    recoveryAttempts.current++;
    console.log(`Tentativa de recuperação ${recoveryAttempts.current}/${maxRecoveryAttempts}`);

    try {
      // Tentar obter sessão atual
      const { data: { session: currentSession }, error } = await supabase.auth.getSession();
      
      if (error) {
        console.warn('Erro ao obter sessão, tentando refresh:', error);
        
        // Tentar refresh da sessão
        const { data: { session: refreshedSession }, error: refreshError } = await supabase.auth.refreshSession();
        
        if (refreshError) {
          console.error('Erro ao fazer refresh da sessão:', refreshError);
          
          // Se falhou, agendar nova tentativa
          if (recoveryAttempts.current < maxRecoveryAttempts) {
            scheduleRetry();
          }
          return;
        }

        if (refreshedSession) {
          console.log('Sessão recuperada com sucesso via refresh');
          await refreshUser();
          recoveryAttempts.current = 0; // Reset tentativas em caso de sucesso
          return;
        }
      }

      if (currentSession) {
        console.log('Sessão recuperada com sucesso');
        await refreshUser();
        recoveryAttempts.current = 0; // Reset tentativas em caso de sucesso
        return;
      }

      // Se chegou aqui, não conseguiu recuperar
      console.warn('Não foi possível recuperar a sessão');
      
      if (recoveryAttempts.current < maxRecoveryAttempts) {
        scheduleRetry();
      }

    } catch (error) {
      console.error('Erro durante tentativa de recuperação:', error);
      
      if (recoveryAttempts.current < maxRecoveryAttempts) {
        scheduleRetry();
      }
    }
  };

  const scheduleRetry = () => {
    // Limpar timeout anterior se existir
    if (recoveryTimeout.current) {
      clearTimeout(recoveryTimeout.current);
    }

    // Agendar nova tentativa com backoff exponencial
    const delay = Math.min(1000 * Math.pow(2, recoveryAttempts.current - 1), 10000);
    
    recoveryTimeout.current = setTimeout(() => {
      attemptRecovery();
    }, delay);
  };

  // Cleanup
  useEffect(() => {
    return () => {
      if (recoveryTimeout.current) {
        clearTimeout(recoveryTimeout.current);
      }
    };
  }, []);

  return {
    isRecovering: recoveryAttempts.current > 0 && recoveryAttempts.current < maxRecoveryAttempts,
    recoveryAttempts: recoveryAttempts.current,
    maxRecoveryAttempts
  };
}
