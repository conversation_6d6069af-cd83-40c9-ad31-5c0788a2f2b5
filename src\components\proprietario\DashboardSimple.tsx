'use client';

import React, { memo } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useEmpresaProprietario } from '@/hooks/useEmpresaProprietario';
import { motion } from 'framer-motion';

interface DashboardSimpleProps {
  className?: string;
}

const DashboardSimple = memo(({ className = '' }: DashboardSimpleProps) => {
  const { user, loading: authLoading } = useAuth();
  const { 
    empresa, 
    loading: empresaLoading, 
    error: empresaError,
    temEmpresa,
    empresaAtiva 
  } = useEmpresaProprietario();

  const isLoading = authLoading || empresaLoading;

  if (isLoading) {
    return (
      <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ${className}`}>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center">
            <div className="animate-spin rounded-full h-16 w-16 border-b-2 border-blue-600 mx-auto mb-4"></div>
            <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
              Carregando Dashboard
            </h2>
            <p className="text-gray-600 dark:text-gray-400">
              Aguarde enquanto carregamos seus dados...
            </p>
          </div>
        </div>
      </div>
    );
  }

  if (empresaError) {
    return (
      <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ${className}`}>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center max-w-md mx-auto p-8">
            <div className="text-6xl mb-6">⚠️</div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Erro ao Carregar Dados
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Não foi possível carregar os dados da empresa.
            </p>
            <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
              <code className="text-sm text-red-700 break-all">
                {empresaError}
              </code>
            </div>
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              Tentar Novamente
            </button>
          </div>
        </div>
      </div>
    );
  }

  if (!temEmpresa) {
    return (
      <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ${className}`}>
        <div className="flex items-center justify-center min-h-screen">
          <div className="text-center max-w-md mx-auto p-8">
            <div className="text-6xl mb-6">🏢</div>
            <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              Empresa Não Encontrada
            </h2>
            <p className="text-gray-600 dark:text-gray-400 mb-6">
              Você precisa criar uma empresa para acessar o dashboard do proprietário.
            </p>
            <button
              onClick={() => window.location.href = '/onboarding'}
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-3 px-6 rounded-lg transition-colors"
            >
              Criar Empresa
            </button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ${className}`}>
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center py-6">
            <div>
              <h1 className="text-3xl font-bold text-gray-900 dark:text-white">
                Dashboard ServiceTech
              </h1>
              <p className="text-gray-600 dark:text-gray-400">
                Bem-vindo, {user?.name || user?.email}
              </p>
            </div>
            <div className={`px-4 py-2 rounded-full text-sm font-medium ${
              empresaAtiva 
                ? 'bg-green-100 text-green-800' 
                : 'bg-yellow-100 text-yellow-800'
            }`}>
              {empresaAtiva ? '✅ Empresa Ativa' : '⚠️ Configuração Pendente'}
            </div>
          </div>
        </div>
      </div>

      {/* Conteúdo Principal */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Informações da Empresa */}
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6 mb-8"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex items-center space-x-4">
            <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center text-white text-2xl font-bold">
              {empresa?.nome_empresa?.charAt(0) || 'E'}
            </div>
            <div className="flex-1">
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
                {empresa?.nome_empresa || 'Nome da Empresa'}
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                {empresa?.segmento || 'Segmento não definido'}
              </p>
              {empresa?.endereco_completo && (
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  📍 {empresa.endereco_completo}
                </p>
              )}
            </div>
          </div>
        </motion.div>

        {/* Cards de Métricas Simples */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          {[
            { title: 'Agendamentos', value: '0', icon: '📅', color: 'blue' },
            { title: 'Receita', value: 'R$ 0,00', icon: '💰', color: 'green' },
            { title: 'Clientes', value: '0', icon: '👥', color: 'purple' },
            { title: 'Serviços', value: '0', icon: '⚡', color: 'orange' }
          ].map((metric, index) => (
            <motion.div
              key={metric.title}
              className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: index * 0.1 }}
            >
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-gray-600 dark:text-gray-400">
                    {metric.title}
                  </p>
                  <p className="text-2xl font-bold text-gray-900 dark:text-white">
                    {metric.value}
                  </p>
                </div>
                <div className="text-3xl">
                  {metric.icon}
                </div>
              </div>
            </motion.div>
          ))}
        </div>

        {/* Ações Rápidas */}
        <motion.div
          className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-6"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5, delay: 0.5 }}
        >
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            🚀 Ações Rápidas
          </h3>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
            {[
              { title: 'Configurar Empresa', href: '/proprietario/empresa', icon: '🏢' },
              { title: 'Gerenciar Serviços', href: '/proprietario/servicos', icon: '⚡' },
              { title: 'Ver Agendamentos', href: '/proprietario/agendamentos', icon: '📅' },
              { title: 'Relatórios', href: '/proprietario/relatorios', icon: '📊' },
              { title: 'Configurações', href: '/proprietario/configuracoes', icon: '⚙️' },
              { title: 'Suporte', href: '/suporte', icon: '💬' }
            ].map((action) => (
              <button
                key={action.title}
                onClick={() => window.location.href = action.href}
                className="flex items-center space-x-3 p-4 border border-gray-200 dark:border-gray-700 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-colors"
              >
                <span className="text-2xl">{action.icon}</span>
                <span className="font-medium text-gray-900 dark:text-white">
                  {action.title}
                </span>
              </button>
            ))}
          </div>
        </motion.div>

        {/* Debug Info */}
        <motion.div
          className="mt-8 bg-gray-50 dark:bg-gray-900 rounded-lg p-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.5, delay: 0.7 }}
        >
          <details className="text-sm">
            <summary className="cursor-pointer font-medium text-gray-700 dark:text-gray-300">
              🔧 Informações de Debug
            </summary>
            <div className="mt-2 space-y-1 text-gray-600 dark:text-gray-400">
              <p>Empresa ID: {empresa?.empresa_id}</p>
              <p>Status: {empresa?.status}</p>
              <p>Usuário: {user?.email}</p>
              <p>Role: {user?.role}</p>
              <p>Timestamp: {new Date().toLocaleString()}</p>
            </div>
          </details>
        </motion.div>
      </div>
    </div>
  );
});

DashboardSimple.displayName = 'DashboardSimple';

export { DashboardSimple };
