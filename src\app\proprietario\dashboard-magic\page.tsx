'use client';

import React, { useState, useEffect } from 'react';
import { DashboardMagicLayout } from '@/components/proprietario/DashboardMagicLayout';
import { DashboardSimple } from '@/components/proprietario/DashboardSimple';
import { FeedbackProvider } from '@/components/ui/enhanced-feedback';
import { useAuth } from '@/contexts/AuthContext';

export default function DashboardMagicPage() {
  const [useSimpleVersion, setUseSimpleVersion] = useState(false);
  const [debugMode, setDebugMode] = useState(false);
  const [renderError, setRenderError] = useState<string | null>(null);
  const { user, loading, initialized } = useAuth();

  useEffect(() => {
    // Verificar parâmetros da URL
    const urlParams = new URLSearchParams(window.location.search);
    setUseSimpleVersion(urlParams.get('simple') === 'true');
    setDebugMode(urlParams.get('debug') === 'true');
  }, []);

  // Error boundary manual
  useEffect(() => {
    const handleError = (error: ErrorEvent) => {
      console.error('Dashboard Error:', error);
      setRenderError(error.message);
      setUseSimpleVersion(true); // Fallback para versão simples
    };

    window.addEventListener('error', handleError);
    return () => window.removeEventListener('error', handleError);
  }, []);

  // Se há erro, mostrar versão simples
  if (renderError) {
    return (
      <FeedbackProvider>
        <DashboardSimple />
      </FeedbackProvider>
    );
  }

  // Modo debug
  if (debugMode) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8">
        <div className="max-w-4xl mx-auto">
          <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
            <h1 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
              🔧 Dashboard Debug Mode
            </h1>

            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
              {/* Estado da Autenticação */}
              <div className="space-y-3">
                <h3 className="font-semibold text-gray-900 dark:text-white">Autenticação</h3>
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span>Loading:</span>
                    <span className={loading ? 'text-yellow-600' : 'text-green-600'}>
                      {loading ? 'true' : 'false'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>Initialized:</span>
                    <span className={initialized ? 'text-green-600' : 'text-red-600'}>
                      {initialized ? 'true' : 'false'}
                    </span>
                  </div>
                  <div className="flex justify-between">
                    <span>User:</span>
                    <span className={user ? 'text-green-600' : 'text-red-600'}>
                      {user ? user.email : 'null'}
                    </span>
                  </div>
                </div>
              </div>

              {/* Controles */}
              <div className="space-y-3">
                <h3 className="font-semibold text-gray-900 dark:text-white">Controles</h3>
                <div className="space-y-2">
                  <button
                    onClick={() => setUseSimpleVersion(!useSimpleVersion)}
                    className={`w-full text-left px-3 py-2 rounded text-sm ${
                      useSimpleVersion
                        ? 'bg-green-100 text-green-800'
                        : 'bg-gray-100 text-gray-800'
                    }`}
                  >
                    Versão Simples: {useSimpleVersion ? 'ON' : 'OFF'}
                  </button>
                  <button
                    onClick={() => window.location.href = '/proprietario/dashboard-magic'}
                    className="w-full text-left px-3 py-2 rounded text-sm bg-blue-100 text-blue-800"
                  >
                    Dashboard Normal
                  </button>
                </div>
              </div>
            </div>

            {/* Renderizar dashboard baseado na escolha */}
            <div className="border border-gray-200 dark:border-gray-700 rounded p-4">
              <h4 className="font-medium mb-2">
                Dashboard Atual: {useSimpleVersion ? 'Simples' : 'Completo'}
              </h4>
              <ErrorBoundary onError={() => setUseSimpleVersion(true)}>
                {useSimpleVersion ? (
                  <DashboardSimple />
                ) : (
                  <FeedbackProvider>
                    <DashboardMagicLayout />
                  </FeedbackProvider>
                )}
              </ErrorBoundary>
            </div>
          </div>
        </div>
      </div>
    );
  }

  // Renderização normal com fallback
  return (
    <ErrorBoundary onError={() => setUseSimpleVersion(true)}>
      {useSimpleVersion ? (
        <FeedbackProvider>
          <DashboardSimple />
        </FeedbackProvider>
      ) : (
        <FeedbackProvider>
          <div className="min-h-screen">
            <DashboardMagicLayout />
          </div>
        </FeedbackProvider>
      )}
    </ErrorBoundary>
  );
}

// Error Boundary Component
class ErrorBoundary extends React.Component<
  { children: React.ReactNode; onError?: () => void },
  { hasError: boolean; error?: Error }
> {
  constructor(props: { children: React.ReactNode; onError?: () => void }) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('Dashboard Error Boundary:', error, errorInfo);
    if (this.props.onError) {
      this.props.onError();
    }
  }

  render() {
    if (this.state.hasError) {
      return (
        <FeedbackProvider>
          <DashboardSimple />
        </FeedbackProvider>
      );
    }

    return this.props.children;
  }
}
