'use client';

import { useSupabaseQuery, useSupabaseMutation, useSupabaseSubscription } from './useSupabaseEnhanced';
import { Empresa, DashboardMetricas, EmpresaMetricas } from '@/types/supabase-enhanced';

// Hook específico para empresa do proprietário
export function useEmpresaProprietarioEnhanced(empresaId?: number) {
  const {
    data: empresas,
    loading,
    error,
    refetch
  } = useSupabaseQuery<Empresa>('empresas', {
    filters: empresaId ? { empresa_id: empresaId } : {},
    cache: true,
    cacheTTL: 10 * 60 * 1000, // 10 minutos
    enabled: true
  });

  const empresa = empresas?.[0] || null;

  return {
    empresa,
    loading,
    error,
    refetch,
    temEmpresa: !!empresa,
    empresaAtiva: empresa?.status === 'ativo'
  };
}

// Hook para métricas do dashboard
export function useDashboardMetricasEnhanced(empresaId: number) {
  const {
    data: metricas,
    loading,
    error,
    refetch,
    fromCache
  } = useSupabaseQuery<DashboardMetricas>('view_dashboard_metricas', {
    filters: { empresa_id: empresaId },
    cache: true,
    cacheTTL: 5 * 60 * 1000, // 5 minutos
    enabled: !!empresaId,
    refetchInterval: 2 * 60 * 1000 // Atualizar a cada 2 minutos
  });

  const metrica = metricas?.[0] || null;

  return {
    metricas: metrica,
    loading,
    error,
    refetch,
    fromCache,
    totalAgendamentos: metrica?.total_agendamentos_mes || 0,
    agendamentosHoje: metrica?.agendamentos_confirmados || 0,
    receitaMensal: metrica?.receita_liquida_mes || 0,
    clientesAtivos: metrica?.total_clientes_ativos || 0,
    taxaCancelamento: metrica?.taxa_cancelamento_mes || 0,
    taxaConfirmacao: metrica?.taxa_confirmacao_mes || 0,
    ticketMedio: metrica?.ticket_medio || 0
  };
}

// Hook para agendamentos com filtros avançados
export function useAgendamentosEnhanced(
  empresaId: number,
  options: {
    status?: string[];
    dataInicio?: string;
    dataFim?: string;
    colaboradorId?: number;
    limit?: number;
    realtime?: boolean;
  } = {}
) {
  const {
    status,
    dataInicio,
    dataFim,
    colaboradorId,
    limit = 50,
    realtime = false
  } = options;

  // Construir filtros
  const filters: Record<string, any> = { empresa_id: empresaId };

  if (status && status.length > 0) {
    filters.status = { operator: 'in', value: status };
  }

  if (dataInicio) {
    filters.data_agendamento = { operator: 'gte', value: dataInicio };
  }

  if (dataFim) {
    filters.data_agendamento = {
      ...filters.data_agendamento,
      operator: 'lte',
      value: dataFim
    };
  }

  if (colaboradorId) {
    filters.colaborador_id = colaboradorId;
  }

  const {
    data: agendamentos,
    loading,
    error,
    refetch
  } = useSupabaseQuery('agendamentos', {
    filters,
    orderBy: [{ column: 'data_agendamento', ascending: false }],
    limit,
    cache: true,
    cacheTTL: 2 * 60 * 1000, // 2 minutos
    enabled: !!empresaId
  });

  // Subscription para atualizações em tempo real
  useSupabaseSubscription('agendamentos', {
    filter: `empresa_id=eq.${empresaId}`,
    enabled: realtime && !!empresaId,
    onData: () => {
      refetch(); // Atualizar dados quando houver mudanças
    }
  });

  return {
    agendamentos: agendamentos || [],
    loading,
    error,
    refetch
  };
}

// Hook para mutações de agendamento
export function useAgendamentoMutations(empresaId: number) {
  const {
    mutate: criarAgendamento,
    loading: criandoAgendamento,
    error: erroCriar
  } = useSupabaseMutation('agendamentos', 'insert', {
    invalidateQueries: ['agendamentos', 'view_dashboard_metricas'],
    onSuccess: (data) => {
      console.log('Agendamento criado:', data);
    }
  });

  const {
    mutate: atualizarAgendamento,
    loading: atualizandoAgendamento,
    error: erroAtualizar
  } = useSupabaseMutation('agendamentos', 'update', {
    invalidateQueries: ['agendamentos', 'view_dashboard_metricas'],
    onSuccess: (data) => {
      console.log('Agendamento atualizado:', data);
    }
  });

  const {
    mutate: cancelarAgendamento,
    loading: cancelandoAgendamento,
    error: erroCancelar
  } = useSupabaseMutation('agendamentos', 'update', {
    invalidateQueries: ['agendamentos', 'view_dashboard_metricas'],
    onSuccess: (data) => {
      console.log('Agendamento cancelado:', data);
    }
  });

  return {
    criarAgendamento: (dados: any) => criarAgendamento({ ...dados, empresa_id: empresaId }),
    atualizarAgendamento,
    cancelarAgendamento: (agendamentoId: number) =>
      cancelarAgendamento({ id: agendamentoId, status: 'cancelado' }),
    loading: criandoAgendamento || atualizandoAgendamento || cancelandoAgendamento,
    errors: {
      criar: erroCriar,
      atualizar: erroAtualizar,
      cancelar: erroCancelar
    }
  };
}

// Hook para serviços da empresa
export function useServicosEmpresa(empresaId: number) {
  const {
    data: servicos,
    loading,
    error,
    refetch
  } = useSupabaseQuery('servicos', {
    filters: { empresa_id: empresaId, ativo: true },
    orderBy: [{ column: 'ordem_exibicao', ascending: true }],
    cache: true,
    cacheTTL: 15 * 60 * 1000, // 15 minutos
    enabled: !!empresaId
  });

  const {
    mutate: criarServico,
    loading: criandoServico
  } = useSupabaseMutation('servicos', 'insert', {
    invalidateQueries: ['servicos']
  });

  const {
    mutate: atualizarServico,
    loading: atualizandoServico
  } = useSupabaseMutation('servicos', 'update', {
    invalidateQueries: ['servicos']
  });

  return {
    servicos: servicos || [],
    loading,
    error,
    refetch,
    criarServico: (dados: any) => criarServico({ ...dados, empresa_id: empresaId }),
    atualizarServico,
    mutationLoading: criandoServico || atualizandoServico
  };
}

// Hook para colaboradores da empresa
export function useColaboradoresEmpresa(empresaId: number) {
  const {
    data: colaboradores,
    loading,
    error,
    refetch
  } = useSupabaseQuery('colaboradores_empresa', {
    filters: { empresa_id: empresaId, ativo: true },
    cache: true,
    cacheTTL: 10 * 60 * 1000, // 10 minutos
    enabled: !!empresaId
  });

  return {
    colaboradores: colaboradores || [],
    loading,
    error,
    refetch
  };
}

// Hook para relatórios avançados
export function useRelatoriosEmpresa(
  empresaId: number,
  periodo: { inicio: string; fim: string }
) {
  const {
    data: relatorio,
    loading,
    error,
    refetch
  } = useSupabaseQuery('get_empresa_metricas', {
    filters: { empresa_id: empresaId },
    cache: true,
    cacheTTL: 30 * 60 * 1000, // 30 minutos
    enabled: !!empresaId && !!periodo.inicio && !!periodo.fim
  });

  return {
    relatorio: relatorio?.[0] as EmpresaMetricas | null,
    loading,
    error,
    refetch
  };
}

interface UseSupabaseQueryResult<T> {
  data: T | null;
  loading: boolean;
  error: string | null;
  refetch: () => Promise<void>;
  mutate: (newData: T) => void;
  invalidate: () => void;
}

export function useSupabaseQuery<T = any>(
  table: string,
  options: UseSupabaseQueryOptions = {}
): UseSupabaseQueryResult<T> {
  const {
    enabled = true,
    refetchOnWindowFocus = true,
    refetchInterval,
    cacheTime = 5 * 60 * 1000, // 5 minutos
    staleTime = 1 * 60 * 1000,  // 1 minuto
    onSuccess,
    onError,
    select = '*',
    orderBy = [],
    filters = {},
    limit,
    offset
  } = options;

  const supabase = createClientComponentClient();
  
  // Criar chave única para o cache baseada nos parâmetros
  const cacheKey = `supabase-${table}-${JSON.stringify({
    select,
    orderBy,
    filters,
    limit,
    offset
  })}`;

  const fetchData = useCallback(async () => {
    if (!enabled) return null;

    let query = supabase.from(table).select(select);

    // Aplicar filtros
    Object.entries(filters).forEach(([key, value]) => {
      if (value !== undefined && value !== null) {
        if (Array.isArray(value)) {
          query = query.in(key, value);
        } else if (typeof value === 'object' && value.operator) {
          // Suporte para operadores customizados
          switch (value.operator) {
            case 'gte':
              query = query.gte(key, value.value);
              break;
            case 'lte':
              query = query.lte(key, value.value);
              break;
            case 'gt':
              query = query.gt(key, value.value);
              break;
            case 'lt':
              query = query.lt(key, value.value);
              break;
            case 'like':
              query = query.like(key, value.value);
              break;
            case 'ilike':
              query = query.ilike(key, value.value);
              break;
            case 'neq':
              query = query.neq(key, value.value);
              break;
            default:
              query = query.eq(key, value.value);
          }
        } else {
          query = query.eq(key, value);
        }
      }
    });

    // Aplicar ordenação
    orderBy.forEach(({ column, ascending = true }) => {
      query = query.order(column, { ascending });
    });

    // Aplicar paginação
    if (limit) {
      query = query.limit(limit);
    }
    if (offset) {
      query = query.range(offset, offset + (limit || 10) - 1);
    }

    const { data, error } = await query;

    if (error) {
      throw new Error(error.message);
    }

    return data;
  }, [
    enabled,
    supabase,
    table,
    select,
    JSON.stringify(filters),
    JSON.stringify(orderBy),
    limit,
    offset
  ]);

  const {
    data,
    loading,
    error,
    refetch,
    mutate,
    invalidate
  } = useApiCache<T>(
    cacheKey,
    fetchData,
    {
      cacheTime,
      staleTime,
      refetchOnWindowFocus,
      refetchInterval
    }
  );

  // Callbacks de sucesso e erro
  useEffect(() => {
    if (data && onSuccess) {
      onSuccess(data);
    }
  }, [data, onSuccess]);

  useEffect(() => {
    if (error && onError) {
      onError(new Error(error));
    }
  }, [error, onError]);

  return {
    data,
    loading,
    error,
    refetch,
    mutate,
    invalidate
  };
}

// Hook para mutações (INSERT, UPDATE, DELETE)
interface UseSupabaseMutationOptions {
  onSuccess?: (data: any) => void;
  onError?: (error: Error) => void;
  invalidateQueries?: string[]; // Chaves de cache para invalidar
}

interface UseSupabaseMutationResult {
  mutate: (data: any) => Promise<any>;
  loading: boolean;
  error: string | null;
}

export function useSupabaseMutation(
  table: string,
  operation: 'insert' | 'update' | 'delete' | 'upsert',
  options: UseSupabaseMutationOptions = {}
): UseSupabaseMutationResult {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClientComponentClient();
  const { onSuccess, onError, invalidateQueries = [] } = options;

  const mutate = useCallback(async (data: any) => {
    setLoading(true);
    setError(null);

    try {
      let result;

      switch (operation) {
        case 'insert':
          result = await supabase.from(table).insert(data).select();
          break;
        case 'update':
          const { id, ...updateData } = data;
          result = await supabase
            .from(table)
            .update(updateData)
            .eq('id', id)
            .select();
          break;
        case 'delete':
          result = await supabase
            .from(table)
            .delete()
            .eq('id', data.id)
            .select();
          break;
        case 'upsert':
          result = await supabase.from(table).upsert(data).select();
          break;
        default:
          throw new Error(`Operação não suportada: ${operation}`);
      }

      if (result.error) {
        throw new Error(result.error.message);
      }

      // Invalidar caches relacionados
      invalidateQueries.forEach(key => {
        // Implementar invalidação de cache aqui
        // Por enquanto, apenas limpar do localStorage se necessário
      });

      if (onSuccess) {
        onSuccess(result.data);
      }

      return result.data;
    } catch (err: any) {
      const errorMessage = err.message || 'Erro na operação';
      setError(errorMessage);
      
      if (onError) {
        onError(new Error(errorMessage));
      }
      
      throw err;
    } finally {
      setLoading(false);
    }
  }, [supabase, table, operation, onSuccess, onError, invalidateQueries]);

  return {
    mutate,
    loading,
    error
  };
}

// Hook para subscription em tempo real
interface UseSupabaseSubscriptionOptions {
  event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
  filter?: string;
  onData?: (payload: any) => void;
  onError?: (error: Error) => void;
}

export function useSupabaseSubscription(
  table: string,
  options: UseSupabaseSubscriptionOptions = {}
) {
  const { event = '*', filter, onData, onError } = options;
  const supabase = createClientComponentClient();
  const subscriptionRef = useRef<any>(null);

  useEffect(() => {
    if (!onData) return;

    let channel = supabase
      .channel(`${table}-changes`)
      .on(
        'postgres_changes',
        {
          event,
          schema: 'public',
          table,
          filter
        },
        (payload) => {
          onData(payload);
        }
      );

    channel.subscribe((status) => {
      if (status === 'SUBSCRIBED') {
        console.log(`Subscribed to ${table} changes`);
      } else if (status === 'CHANNEL_ERROR') {
        console.error(`Error subscribing to ${table} changes`);
        if (onError) {
          onError(new Error(`Subscription error for table ${table}`));
        }
      }
    });

    subscriptionRef.current = channel;

    return () => {
      if (subscriptionRef.current) {
        supabase.removeChannel(subscriptionRef.current);
      }
    };
  }, [supabase, table, event, filter, onData, onError]);

  const unsubscribe = useCallback(() => {
    if (subscriptionRef.current) {
      supabase.removeChannel(subscriptionRef.current);
      subscriptionRef.current = null;
    }
  }, [supabase]);

  return { unsubscribe };
}

// Hook para upload de arquivos
interface UseSupabaseStorageOptions {
  bucket: string;
  onProgress?: (progress: number) => void;
  onSuccess?: (url: string) => void;
  onError?: (error: Error) => void;
}

export function useSupabaseStorage(options: UseSupabaseStorageOptions) {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const [error, setError] = useState<string | null>(null);
  const supabase = createClientComponentClient();
  const { bucket, onProgress, onSuccess, onError } = options;

  const upload = useCallback(async (file: File, path?: string) => {
    setUploading(true);
    setError(null);
    setProgress(0);

    try {
      const fileName = path || `${Date.now()}-${file.name}`;
      
      const { data, error: uploadError } = await supabase.storage
        .from(bucket)
        .upload(fileName, file, {
          cacheControl: '3600',
          upsert: false
        });

      if (uploadError) {
        throw new Error(uploadError.message);
      }

      // Obter URL pública
      const { data: { publicUrl } } = supabase.storage
        .from(bucket)
        .getPublicUrl(fileName);

      setProgress(100);
      
      if (onSuccess) {
        onSuccess(publicUrl);
      }

      return { url: publicUrl, path: fileName };
    } catch (err: any) {
      const errorMessage = err.message || 'Erro no upload';
      setError(errorMessage);
      
      if (onError) {
        onError(new Error(errorMessage));
      }
      
      throw err;
    } finally {
      setUploading(false);
    }
  }, [supabase, bucket, onSuccess, onError]);

  // Simular progresso para melhor UX
  useEffect(() => {
    if (uploading && progress < 90) {
      const timer = setTimeout(() => {
        setProgress(prev => Math.min(prev + 10, 90));
        if (onProgress) {
          onProgress(progress);
        }
      }, 200);
      return () => clearTimeout(timer);
    }
  }, [uploading, progress, onProgress]);

  return {
    upload,
    uploading,
    progress,
    error
  };
}
