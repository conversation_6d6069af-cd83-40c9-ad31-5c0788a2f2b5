'use client';

import { useCallback } from 'react';
import { useEmpresaProprietario } from './useEmpresaProprietario';

export function useAcoesRapidas() {
  const { metricas, loading, error } = useEmpresaProprietario();

  const executarAcao = useCallback(async (acao: string) => {
    switch (acao) {
      case 'backup':
        // Simular ação de backup
        console.log('Executando backup dos dados...');
        // Aqui seria implementada a lógica real de backup
        break;
      case 'relatorio':
        // Simular geração de relatório
        console.log('Gerando relatório...');
        break;
      default:
        console.log(`Ação não reconhecida: ${acao}`);
    }
  }, []);

  return {
    acoes: [], // Lista de ações disponíveis (pode ser expandida)
    loading,
    error,
    agendamentosHoje: metricas?.agendamentos_pendentes || 0,
    notificacoesPendentes: 3, // Valor simulado
    tarefasPendentes: metricas?.proximos_vencimentos || 0,
    executarAcao
  };
}
