'use client';

import React from 'react';
import Link from 'next/link';
import { motion } from 'framer-motion';
import { MagicCard } from '@/components/ui/magic-card';
import { TextAnimate } from '@/components/ui/text-animate';
import { ShimmerButton } from '@/components/ui/shimmer-button';

interface EmpresaSetupGuideProps {
  className?: string;
}

export function EmpresaSetupGuide({ className = '' }: EmpresaSetupGuideProps) {
  const passos = [
    {
      numero: 1,
      titulo: 'Criar Empresa',
      descricao: 'Configure os dados básicos da sua empresa',
      icone: '🏢',
      href: '/proprietario/empresa/criar'
    },
    {
      numero: 2,
      titulo: 'Adicionar Serviços',
      descricao: 'Cadastre os serviços que sua empresa oferece',
      icone: '⚙️',
      href: '/proprietario/servicos'
    },
    {
      numero: 3,
      titulo: 'Configurar Horários',
      descricao: 'Defina os horários de funcionamento',
      icone: '🕐',
      href: '/proprietario/horarios'
    },
    {
      numero: 4,
      titulo: 'Integrar Pagamentos',
      descricao: 'Configure o Stripe para receber pagamentos',
      icone: '💳',
      href: '/proprietario/pagamentos'
    }
  ];

  return (
    <div className={`space-y-8 ${className}`}>
      {/* Header */}
      <MagicCard className="p-8 text-center" gradientColor="#3B82F6">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          className="space-y-4"
        >
          <div className="text-6xl">🚀</div>
          <TextAnimate
            animation="blurInUp"
            className="text-2xl font-bold text-white"
          >
            Bem-vindo ao ServiceTech!
          </TextAnimate>
          <p className="text-white/90 text-lg">
            Para começar a usar o dashboard, você precisa configurar sua empresa.
          </p>
        </motion.div>
      </MagicCard>

      {/* Passos */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {passos.map((passo, index) => (
          <motion.div
            key={passo.numero}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <MagicCard className="p-6 h-full" gradientColor="#10B981">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="w-12 h-12 bg-white rounded-full flex items-center justify-center text-2xl">
                    {passo.icone}
                  </div>
                </div>
                <div className="flex-1">
                  <div className="flex items-center space-x-2 mb-2">
                    <span className="bg-white text-green-600 text-xs font-bold px-2 py-1 rounded-full">
                      Passo {passo.numero}
                    </span>
                  </div>
                  <h3 className="text-lg font-semibold text-white mb-2">
                    {passo.titulo}
                  </h3>
                  <p className="text-white/80 text-sm mb-4">
                    {passo.descricao}
                  </p>
                  <Link href={passo.href}>
                    <ShimmerButton 
                      className="w-full bg-white text-green-600 hover:bg-gray-100"
                      shimmerColor="#10B981"
                    >
                      Configurar
                    </ShimmerButton>
                  </Link>
                </div>
              </div>
            </MagicCard>
          </motion.div>
        ))}
      </div>

      {/* Ação Principal */}
      <MagicCard className="p-6 text-center" gradientColor="#F59E0B">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.5 }}
          className="space-y-4"
        >
          <div className="text-4xl">⚡</div>
          <h3 className="text-xl font-semibold text-white">
            Pronto para começar?
          </h3>
          <p className="text-white/90">
            Comece criando sua empresa e configure tudo em poucos minutos.
          </p>
          <Link href="/proprietario/empresa/criar">
            <ShimmerButton 
              className="bg-white text-yellow-600 hover:bg-gray-100 px-8 py-3 text-lg font-semibold"
              shimmerColor="#F59E0B"
            >
              Criar Minha Empresa
            </ShimmerButton>
          </Link>
        </motion.div>
      </MagicCard>

      {/* Ajuda */}
      <div className="text-center">
        <p className="text-gray-600 dark:text-gray-400 text-sm">
          Precisa de ajuda? {' '}
          <Link href="/suporte" className="text-blue-600 hover:text-blue-700 underline">
            Entre em contato com o suporte
          </Link>
        </p>
      </div>
    </div>
  );
}
