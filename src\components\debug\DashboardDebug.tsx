'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { useEmpresaProprietario } from '@/hooks/useEmpresaProprietario';
import { useAuth } from '@/contexts/AuthContext';
import { ApiTester } from './ApiTester';

interface DashboardDebugProps {
  className?: string;
}

export function DashboardDebug({ className = '' }: DashboardDebugProps) {
  const [showDebug, setShowDebug] = useState(false);
  const { user, loading: authLoading } = useAuth();
  const {
    empresa,
    planoSaas,
    metricas,
    statusConfiguracao,
    loading,
    error,
    temEmpresa,
    empresaAtiva
  } = useEmpresaProprietario();

  if (!showDebug) {
    return (
      <div className={`fixed bottom-4 left-4 z-50 ${className}`}>
        <button
          onClick={() => setShowDebug(true)}
          className="bg-red-500 hover:bg-red-600 text-white px-3 py-2 rounded-lg text-sm font-medium shadow-lg"
        >
          🐛 Debug Dashboard
        </button>
      </div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      className={`fixed inset-4 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg shadow-2xl z-50 overflow-auto ${className}`}
    >
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white">
            🐛 Dashboard Debug Info
          </h2>
          <button
            onClick={() => setShowDebug(false)}
            className="text-gray-500 hover:text-gray-700 dark:text-gray-400 dark:hover:text-gray-200"
          >
            ✕
          </button>
        </div>

        <div className="space-y-6">
          {/* Autenticação */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
              🔐 Autenticação
            </h3>
            <div className="space-y-2 text-sm">
              <div>
                <span className="font-medium">Loading:</span> {authLoading ? '✅ Sim' : '❌ Não'}
              </div>
              <div>
                <span className="font-medium">User:</span> {user ? '✅ Autenticado' : '❌ Não autenticado'}
              </div>
              {user && (
                <>
                  <div>
                    <span className="font-medium">User ID:</span> {user.id}
                  </div>
                  <div>
                    <span className="font-medium">Email:</span> {user.email}
                  </div>
                  <div>
                    <span className="font-medium">Role:</span> {user.user_metadata?.role || 'Não definido'}
                  </div>
                </>
              )}
            </div>
          </div>

          {/* Hook useEmpresaProprietario */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
              🏢 Hook useEmpresaProprietario
            </h3>
            <div className="space-y-2 text-sm">
              <div>
                <span className="font-medium">Loading:</span> {loading ? '✅ Sim' : '❌ Não'}
              </div>
              <div>
                <span className="font-medium">Error:</span> {error ? `❌ ${error}` : '✅ Nenhum'}
              </div>
              <div>
                <span className="font-medium">Tem Empresa:</span> {temEmpresa ? '✅ Sim' : '❌ Não'}
              </div>
              <div>
                <span className="font-medium">Empresa Ativa:</span> {empresaAtiva ? '✅ Sim' : '❌ Não'}
              </div>
            </div>
          </div>

          {/* Dados da Empresa */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
              🏢 Dados da Empresa
            </h3>
            <div className="text-sm">
              {empresa ? (
                <div className="space-y-2">
                  <div><span className="font-medium">ID:</span> {empresa.empresa_id}</div>
                  <div><span className="font-medium">Nome:</span> {empresa.nome_empresa}</div>
                  <div><span className="font-medium">Status:</span> {empresa.status}</div>
                  <div><span className="font-medium">CNPJ:</span> {empresa.cnpj || 'Não informado'}</div>
                  <div><span className="font-medium">Telefone:</span> {empresa.telefone || 'Não informado'}</div>
                  <div><span className="font-medium">Stripe Customer:</span> {empresa.stripe_customer_id ? '✅ Configurado' : '❌ Não configurado'}</div>
                </div>
              ) : (
                <div className="text-red-600 dark:text-red-400">❌ Nenhum dado de empresa encontrado</div>
              )}
            </div>
          </div>

          {/* Plano SaaS */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
              💳 Plano SaaS
            </h3>
            <div className="text-sm">
              {planoSaas ? (
                <div className="space-y-2">
                  <div><span className="font-medium">Nome:</span> {planoSaas.nome_plano}</div>
                  <div><span className="font-medium">Preço:</span> R$ {planoSaas.preco_mensal}</div>
                  <div><span className="font-medium">Status:</span> {planoSaas.status_assinatura}</div>
                  <div><span className="font-medium">Data Fim:</span> {planoSaas.data_fim || 'Não definida'}</div>
                </div>
              ) : (
                <div className="text-yellow-600 dark:text-yellow-400">⚠️ Nenhum plano SaaS encontrado</div>
              )}
            </div>
          </div>

          {/* Métricas */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
              📊 Métricas
            </h3>
            <div className="text-sm">
              {metricas ? (
                <div className="space-y-2">
                  <div><span className="font-medium">Total Agendamentos:</span> {metricas.total_agendamentos_mes}</div>
                  <div><span className="font-medium">Receita Bruta:</span> R$ {metricas.receita_bruta_mes}</div>
                  <div><span className="font-medium">Clientes Ativos:</span> {metricas.total_clientes_ativos}</div>
                  <div><span className="font-medium">Agendamentos Pendentes:</span> {metricas.agendamentos_pendentes}</div>
                  <div><span className="font-medium">Taxa Confirmação:</span> {metricas.taxa_confirmacao_mes}%</div>
                </div>
              ) : (
                <div className="text-yellow-600 dark:text-yellow-400">⚠️ Nenhuma métrica encontrada</div>
              )}
            </div>
          </div>

          {/* Status Configuração */}
          <div className="bg-gray-50 dark:bg-gray-700 p-4 rounded-lg">
            <h3 className="font-semibold text-gray-900 dark:text-white mb-2">
              ⚙️ Status Configuração
            </h3>
            <div className="text-sm">
              {statusConfiguracao ? (
                <div className="space-y-2">
                  <div><span className="font-medium">Empresa Configurada:</span> {statusConfiguracao.empresa_configurada ? '✅' : '❌'}</div>
                  <div><span className="font-medium">Stripe Configurado:</span> {statusConfiguracao.stripe_configurado ? '✅' : '❌'}</div>
                  <div><span className="font-medium">Serviços Cadastrados:</span> {statusConfiguracao.servicos_cadastrados ? '✅' : '❌'}</div>
                  <div><span className="font-medium">Horários Definidos:</span> {statusConfiguracao.horarios_definidos ? '✅' : '❌'}</div>
                  <div><span className="font-medium">Colaboradores Ativos:</span> {statusConfiguracao.colaboradores_ativos ? '✅' : '❌'}</div>
                  <div><span className="font-medium">% Conclusão:</span> {statusConfiguracao.percentual_conclusao}%</div>
                  <div><span className="font-medium">Próximos Passos:</span> {statusConfiguracao.proximos_passos.length} itens</div>
                </div>
              ) : (
                <div className="text-yellow-600 dark:text-yellow-400">⚠️ Status de configuração não encontrado</div>
              )}
            </div>
          </div>

          {/* Testador de API Avançado */}
          <ApiTester />
        </div>
      </div>
    </motion.div>
  );
}
