#!/usr/bin/env node

/**
 * Script para remover imports não utilizados
 * Baseado no relatório de análise de performance
 */

const fs = require('fs');
const path = require('path');

// Lista de imports não utilizados identificados pelo relatório
const unusedImports = [
  {
    file: 'src/app/api/assinaturas-cliente/route.ts',
    imports: ['FiltrosAssinaturas']
  },
  {
    file: 'src/app/api/planos-assinatura/route.ts',
    imports: ['FiltrosAssinaturas']
  },
  {
    file: 'src/app/api/relatorios/agendamentos/route.ts',
    imports: ['FiltrosRelatorio']
  },
  {
    file: 'src/app/api/relatorios/financeiro/route.ts',
    imports: ['FiltrosRelatorio']
  },
  {
    file: 'src/app/api/testes-usuario/cenarios/route.ts',
    imports: ['CenarioTeste']
  },
  {
    file: 'src/app/api/testes-usuario/sessoes/route.ts',
    imports: ['SessaoTeste']
  },
  {
    file: 'src/app/cliente/assinaturas/page.tsx',
    imports: ['Calendar']
  },
  {
    file: 'src/app/onboarding/selecao-plano/page.tsx',
    imports: ['PLANOS']
  },
  {
    file: 'src/app/pagamento-sucesso/page.tsx',
    imports: ['createClient']
  },
  {
    file: 'src/app/proprietario/agendamentos/page.tsx',
    imports: ['CardHeader', 'CardTitle']
  },
  {
    file: 'src/components/agendamento/ResumoAgendamento.tsx',
    imports: ['CardTitle']
  },
  {
    file: 'src/components/agendamento/SeletorHorario.tsx',
    imports: ['useEffect']
  },
  {
    file: 'src/components/agendamento/SeletorServicosMultiplos.tsx',
    imports: ['useEffect']
  },
  {
    file: 'src/components/agendamentos/FiltrosAgendamentos.tsx',
    imports: ['StatusAgendamento', 'StatusPagamento', 'FormaPagamento']
  },
  {
    file: 'src/components/agendamentos/__tests__/CardAgendamento.test.tsx',
    imports: ['waitFor']
  },
  {
    file: 'src/components/combos/FormularioCombo.tsx',
    imports: ['Servico']
  },
  {
    file: 'src/components/marketing/GerenciamentoCampanhas.tsx',
    imports: ['CampanhaMarketing']
  },
  {
    file: 'src/components/monitoring/MonitoringDashboard.tsx',
    imports: ['AlertTriangle', 'Database']
  },
  {
    file: 'src/components/relatorios/FiltrosPeriodo.tsx',
    imports: ['startOfDay', 'endOfDay']
  },
  {
    file: 'src/components/ui/Button.tsx',
    imports: ['type VariantProps']
  },
  {
    file: 'src/hooks/useColaboradores.ts',
    imports: ['useEffect']
  },
  {
    file: 'src/hooks/useCombos.ts',
    imports: ['Combo']
  },
  {
    file: 'src/hooks/useGerenciamentoAgendamentos.ts',
    imports: ['Agendamento', 'StatusPagamento']
  },
  {
    file: 'src/hooks/useNotifications.ts',
    imports: ['TipoNotificacao']
  },
  {
    file: 'src/hooks/usePaymentStatusPolling.ts',
    imports: ['useEffect']
  },
  {
    file: 'src/hooks/useServicos.ts',
    imports: ['useMemo']
  },
  {
    file: 'src/hooks/useTestesUsuario.ts',
    imports: ['useEffect']
  },
  {
    file: 'src/utils/busca.ts',
    imports: ['EmpresaBusca']
  },
  {
    file: 'src/utils/marketing.ts',
    imports: ['ResultadoAplicacaoCupom']
  }
];

function removeUnusedImports() {
  console.log('🧹 Iniciando remoção de imports não utilizados...\n');
  
  let totalRemoved = 0;
  let filesProcessed = 0;
  
  for (const item of unusedImports) {
    const filePath = path.join(__dirname, '..', item.file);
    
    if (!fs.existsSync(filePath)) {
      console.log(`⚠️  Arquivo não encontrado: ${item.file}`);
      continue;
    }
    
    try {
      let content = fs.readFileSync(filePath, 'utf8');
      let modified = false;
      
      for (const importName of item.imports) {
        // Padrões para encontrar e remover imports
        const patterns = [
          // Import único: import { ImportName } from '...';
          new RegExp(`import\\s*{\\s*${importName}\\s*}\\s*from\\s*['"'][^'"]+['"];?\\s*\\n?`, 'g'),
          // Import em lista: , ImportName
          new RegExp(`,\\s*${importName}(?=\\s*[,}])`, 'g'),
          // Import em lista: ImportName,
          new RegExp(`${importName}\\s*,`, 'g'),
          // Import sozinho em lista: { ImportName }
          new RegExp(`{\\s*${importName}\\s*}`, 'g'),
          // Type import: type ImportName
          new RegExp(`type\\s+${importName}(?=\\s*[,}])`, 'g')
        ];
        
        for (const pattern of patterns) {
          const beforeReplace = content;
          content = content.replace(pattern, (match) => {
            // Se for uma vírgula, remover apenas a vírgula e o import
            if (match.includes(',')) {
              return match.replace(new RegExp(`\\s*,?\\s*${importName}\\s*,?`), '');
            }
            // Se for um import completo, remover a linha toda
            return '';
          });
          
          if (content !== beforeReplace) {
            modified = true;
            console.log(`  ✅ Removido: ${importName}`);
            totalRemoved++;
          }
        }
      }
      
      // Limpar linhas vazias extras e imports vazios
      content = content
        .replace(/import\s*{\s*}\s*from\s*['"'][^'"]+['"];?\s*\n?/g, '') // Remove imports vazios
        .replace(/\n\s*\n\s*\n/g, '\n\n') // Remove linhas vazias extras
        .replace(/^import\s*{\s*,/gm, 'import {') // Remove vírgulas no início
        .replace(/,\s*}\s*from/g, ' } from'); // Remove vírgulas antes do fechamento
      
      if (modified) {
        fs.writeFileSync(filePath, content, 'utf8');
        console.log(`📝 Arquivo atualizado: ${item.file}\n`);
        filesProcessed++;
      }
      
    } catch (error) {
      console.error(`❌ Erro ao processar ${item.file}:`, error.message);
    }
  }
  
  console.log(`\n🎉 Limpeza concluída!`);
  console.log(`📊 Estatísticas:`);
  console.log(`   • Imports removidos: ${totalRemoved}`);
  console.log(`   • Arquivos processados: ${filesProcessed}`);
  console.log(`   • Arquivos verificados: ${unusedImports.length}`);
}

// Executar se chamado diretamente
if (require.main === module) {
  removeUnusedImports();
}

module.exports = { removeUnusedImports };
