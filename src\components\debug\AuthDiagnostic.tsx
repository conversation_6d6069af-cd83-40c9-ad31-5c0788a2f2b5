'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { createClient } from '@/utils/supabase/client';
import { Card, CardHeader, CardTitle, CardContent } from '@/components/ui/Card';
import { Button } from '@/components/ui/Button';

interface AuthDiagnosticProps {
  show?: boolean;
}

export function AuthDiagnostic({ show = false }: AuthDiagnosticProps) {
  const { user, session, loading, initialized } = useAuth();
  const [diagnosticData, setDiagnosticData] = useState<any>(null);
  const [isVisible, setIsVisible] = useState(show);
  const supabase = createClient();

  const runDiagnostic = async () => {
    try {
      // Verificar sessão atual
      const { data: { session: currentSession }, error: sessionError } = await supabase.auth.getSession();
      
      // Verificar usuário atual
      const { data: { user: currentUser }, error: userError } = await supabase.auth.getUser();
      
      // Verificar cookies
      const cookies = document.cookie.split(';').reduce((acc, cookie) => {
        const [key, value] = cookie.trim().split('=');
        if (key.includes('supabase') || key.includes('sb-')) {
          acc[key] = value ? value.substring(0, 50) + '...' : 'empty';
        }
        return acc;
      }, {} as Record<string, string>);

      // Verificar localStorage
      const localStorageKeys = [];
      for (let i = 0; i < localStorage.length; i++) {
        const key = localStorage.key(i);
        if (key && (key.includes('supabase') || key.includes('sb-'))) {
          localStorageKeys.push(key);
        }
      }

      setDiagnosticData({
        timestamp: new Date().toISOString(),
        authContext: {
          user: user ? {
            id: user.id,
            email: user.email,
            role: user.role,
            name: user.name
          } : null,
          session: session ? {
            access_token: session.access_token?.substring(0, 50) + '...',
            refresh_token: session.refresh_token?.substring(0, 30) + '...',
            expires_at: session.expires_at,
            user_id: session.user?.id
          } : null,
          loading,
          initialized
        },
        supabaseSession: {
          session: currentSession ? {
            access_token: currentSession.access_token?.substring(0, 50) + '...',
            refresh_token: currentSession.refresh_token?.substring(0, 30) + '...',
            expires_at: currentSession.expires_at,
            user_id: currentSession.user?.id
          } : null,
          error: sessionError?.message
        },
        supabaseUser: {
          user: currentUser ? {
            id: currentUser.id,
            email: currentUser.email,
            role: currentUser.user_metadata?.role
          } : null,
          error: userError?.message
        },
        cookies,
        localStorage: localStorageKeys,
        url: window.location.href,
        userAgent: navigator.userAgent.substring(0, 100) + '...'
      });
    } catch (error) {
      console.error('Erro ao executar diagnóstico:', error);
      setDiagnosticData({
        error: error instanceof Error ? error.message : 'Erro desconhecido'
      });
    }
  };

  useEffect(() => {
    if (isVisible) {
      runDiagnostic();
    }
  }, [isVisible]);

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          variant="outline"
          size="sm"
          className="bg-yellow-100 border-yellow-300 text-yellow-800 hover:bg-yellow-200"
        >
          🔍 Debug Auth
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center p-4">
      <Card className="max-w-4xl w-full max-h-[90vh] overflow-auto">
        <CardHeader>
          <div className="flex justify-between items-center">
            <CardTitle className="text-lg font-bold">
              🔍 Diagnóstico de Autenticação
            </CardTitle>
            <div className="flex gap-2">
              <Button onClick={runDiagnostic} size="sm" variant="outline">
                🔄 Atualizar
              </Button>
              <Button onClick={() => setIsVisible(false)} size="sm" variant="outline">
                ✕ Fechar
              </Button>
            </div>
          </div>
        </CardHeader>
        <CardContent>
          {diagnosticData ? (
            <div className="space-y-4">
              <div className="text-xs text-gray-500">
                Última atualização: {new Date(diagnosticData.timestamp).toLocaleString()}
              </div>
              
              <pre className="bg-gray-100 p-4 rounded-md text-xs overflow-auto max-h-96">
                {JSON.stringify(diagnosticData, null, 2)}
              </pre>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                <div>
                  <h4 className="font-semibold mb-2">Status Rápido:</h4>
                  <ul className="space-y-1">
                    <li className={`flex items-center gap-2 ${user ? 'text-green-600' : 'text-red-600'}`}>
                      {user ? '✅' : '❌'} Usuário no Context
                    </li>
                    <li className={`flex items-center gap-2 ${session ? 'text-green-600' : 'text-red-600'}`}>
                      {session ? '✅' : '❌'} Sessão no Context
                    </li>
                    <li className={`flex items-center gap-2 ${diagnosticData.supabaseSession.session ? 'text-green-600' : 'text-red-600'}`}>
                      {diagnosticData.supabaseSession.session ? '✅' : '❌'} Sessão no Supabase
                    </li>
                    <li className={`flex items-center gap-2 ${initialized ? 'text-green-600' : 'text-orange-600'}`}>
                      {initialized ? '✅' : '⏳'} Context Inicializado
                    </li>
                  </ul>
                </div>
                
                <div>
                  <h4 className="font-semibold mb-2">Ações:</h4>
                  <div className="space-y-2">
                    <Button 
                      onClick={() => window.location.href = '/login'} 
                      size="sm" 
                      variant="outline"
                      className="w-full"
                    >
                      🔄 Ir para Login
                    </Button>
                    <Button 
                      onClick={() => window.location.reload()} 
                      size="sm" 
                      variant="outline"
                      className="w-full"
                    >
                      🔄 Recarregar Página
                    </Button>
                    <Button 
                      onClick={() => {
                        localStorage.clear();
                        window.location.href = '/login';
                      }} 
                      size="sm" 
                      variant="outline"
                      className="w-full text-red-600"
                    >
                      🗑️ Limpar Storage
                    </Button>
                  </div>
                </div>
              </div>
            </div>
          ) : (
            <div className="text-center py-8">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <p>Executando diagnóstico...</p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

// Hook para usar o diagnóstico facilmente
export function useAuthDiagnostic() {
  const [showDiagnostic, setShowDiagnostic] = useState(false);

  // Atalho de teclado para abrir diagnóstico (Ctrl+Shift+D)
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'D') {
        event.preventDefault();
        setShowDiagnostic(true);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    return () => window.removeEventListener('keydown', handleKeyDown);
  }, []);

  return {
    showDiagnostic,
    setShowDiagnostic,
    DiagnosticComponent: () => <AuthDiagnostic show={showDiagnostic} />
  };
}
