'use client';

import React, { memo, useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { MagicCard } from './magic-card';
import { NumberTicker } from './number-ticker';
import { AnimatedCircularProgressBar } from './animated-circular-progress-bar';
import { TextAnimate } from './text-animate';
import { ShimmerButton } from './shimmer-button';

interface MetricData {
  current: number;
  previous?: number;
  target?: number;
  unit?: string;
  prefix?: string;
  suffix?: string;
  decimalPlaces?: number;
}

interface AdvancedMetricCardProps {
  title: string;
  data: MetricData;
  icon: string;
  color: string;
  gradientColor: string;
  description?: string;
  trend?: 'up' | 'down' | 'neutral';
  showComparison?: boolean;
  showProgress?: boolean;
  className?: string;
  delay?: number;
  onDetailsClick?: () => void;
}

const AdvancedMetricCard = memo(({
  title,
  data,
  icon,
  color,
  gradientColor,
  description,
  trend,
  showComparison = true,
  showProgress = false,
  className = '',
  delay = 0,
  onDetailsClick
}: Readonly<AdvancedMetricCardProps>) => {
  const [isHovered, setIsHovered] = useState(false);
  const [showDetails, setShowDetails] = useState(false);

  // Calcular mudança percentual
  const percentageChange = data.previous 
    ? ((data.current - data.previous) / data.previous) * 100 
    : 0;

  // Calcular progresso em relação ao target
  const progressPercentage = data.target 
    ? Math.min((data.current / data.target) * 100, 100)
    : 0;

  const getTrendConfig = () => {
    if (trend === 'up' || percentageChange > 0) {
      return {
        icon: '📈',
        color: 'text-green-600 dark:text-green-400',
        bgColor: 'bg-green-100 dark:bg-green-900/30',
        symbol: '+'
      };
    } else if (trend === 'down' || percentageChange < 0) {
      return {
        icon: '📉',
        color: 'text-red-600 dark:text-red-400',
        bgColor: 'bg-red-100 dark:bg-red-900/30',
        symbol: ''
      };
    } else {
      return {
        icon: '➡️',
        color: 'text-gray-600 dark:text-gray-400',
        bgColor: 'bg-gray-100 dark:bg-gray-900/30',
        symbol: ''
      };
    }
  };

  const trendConfig = getTrendConfig();

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      onHoverStart={() => setIsHovered(true)}
      onHoverEnd={() => setIsHovered(false)}
      className={className}
    >
      <MagicCard
        className="p-6 h-full relative overflow-hidden"
        gradientColor={gradientColor}
        gradientSize={isHovered ? 300 : 200}
      >
        {/* Header */}
        <div className="flex items-start justify-between mb-4">
          <div className="flex items-center space-x-3">
            <motion.div
              className="text-2xl"
              animate={{ 
                scale: isHovered ? 1.1 : 1,
                rotate: isHovered ? [0, 5, -5, 0] : 0
              }}
              transition={{ duration: 0.3 }}
            >
              {icon}
            </motion.div>
            <div>
              <TextAnimate
                animation="blurInUp"
                className="text-sm font-medium text-gray-600 dark:text-gray-400"
              >
                {title}
              </TextAnimate>
              {description && (
                <p className="text-xs text-gray-500 dark:text-gray-500 mt-1">
                  {description}
                </p>
              )}
            </div>
          </div>

          {onDetailsClick && (
            <motion.button
              onClick={onDetailsClick}
              className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
              whileHover={{ scale: 1.1 }}
              whileTap={{ scale: 0.9 }}
            >
              📊
            </motion.button>
          )}
        </div>

        {/* Valor Principal */}
        <div className="mb-4">
          <div className="flex items-baseline space-x-1">
            {data.prefix && (
              <span className="text-lg font-medium text-gray-700 dark:text-gray-300">
                {data.prefix}
              </span>
            )}
            <motion.div
              className="text-3xl font-bold"
              style={{ color }}
              whileHover={{ scale: 1.05 }}
            >
              <NumberTicker
                value={data.current}
                decimalPlaces={data.decimalPlaces ?? 0}
                delay={delay + 0.2}
              />
            </motion.div>
            {data.suffix && (
              <span className="text-lg font-medium text-gray-700 dark:text-gray-300">
                {data.suffix}
              </span>
            )}
          </div>
        </div>

        {/* Comparação e Tendência */}
        {showComparison && data.previous !== undefined && (
          <motion.div
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: delay + 0.4 }}
            className="flex items-center space-x-2 mb-3"
          >
            <div className={`px-2 py-1 rounded-full ${trendConfig.bgColor} flex items-center space-x-1`}>
              <span className="text-xs">{trendConfig.icon}</span>
              <span className={`text-xs font-medium ${trendConfig.color}`}>
                {trendConfig.symbol}{Math.abs(percentageChange).toFixed(1)}%
              </span>
            </div>
            <span className="text-xs text-gray-500 dark:text-gray-400">
              vs período anterior
            </span>
          </motion.div>
        )}

        {/* Barra de Progresso Circular */}
        {showProgress && data.target && (
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: delay + 0.6 }}
            className="flex items-center justify-between"
          >
            <div className="flex-1">
              <div className="flex items-center justify-between text-xs text-gray-600 dark:text-gray-400 mb-1">
                <span>Progresso</span>
                <span>{progressPercentage.toFixed(0)}%</span>
              </div>
              <div className="w-full bg-gray-200 dark:bg-gray-700 rounded-full h-2">
                <motion.div
                  className="h-2 rounded-full"
                  style={{ backgroundColor: color }}
                  initial={{ width: 0 }}
                  animate={{ width: `${progressPercentage}%` }}
                  transition={{ duration: 1, delay: delay + 0.8 }}
                />
              </div>
              <div className="flex justify-between text-xs text-gray-500 dark:text-gray-500 mt-1">
                <span>0</span>
                <span>Meta: {data.target}</span>
              </div>
            </div>
          </motion.div>
        )}

        {/* Detalhes Expandidos */}
        <AnimatePresence>
          {showDetails && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: 'auto' }}
              exit={{ opacity: 0, height: 0 }}
              className="mt-4 pt-4 border-t border-gray-200 dark:border-gray-700"
            >
              <div className="space-y-2 text-xs">
                {data.previous && (
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Anterior:</span>
                    <span className="font-medium">{data.previous}</span>
                  </div>
                )}
                {data.target && (
                  <div className="flex justify-between">
                    <span className="text-gray-600 dark:text-gray-400">Meta:</span>
                    <span className="font-medium">{data.target}</span>
                  </div>
                )}
                <div className="flex justify-between">
                  <span className="text-gray-600 dark:text-gray-400">Variação:</span>
                  <span className={`font-medium ${trendConfig.color}`}>
                    {trendConfig.symbol}{Math.abs(percentageChange).toFixed(2)}%
                  </span>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Botão para mostrar detalhes */}
        {(data.previous !== undefined || data.target !== undefined) && (
          <motion.div
            className="mt-4"
            initial={{ opacity: 0 }}
            animate={{ opacity: isHovered ? 1 : 0 }}
            transition={{ duration: 0.2 }}
          >
            <ShimmerButton
              onClick={() => setShowDetails(!showDetails)}
              className="w-full text-xs py-2"
              shimmerColor={gradientColor}
              background={`${gradientColor}20`}
            >
              {showDetails ? 'Ocultar Detalhes' : 'Ver Detalhes'}
            </ShimmerButton>
          </motion.div>
        )}

        {/* Efeito de partículas no hover */}
        <AnimatePresence>
          {isHovered && (
            <motion.div
              className="absolute inset-0 pointer-events-none"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {[...Array(5)].map((_, i) => (
                <motion.div
                  key={`particle-${i}`}
                  className="absolute w-1 h-1 rounded-full"
                  style={{ backgroundColor: color }}
                  initial={{
                    x: Math.random() * 100 + '%',
                    y: Math.random() * 100 + '%',
                    scale: 0
                  }}
                  animate={{
                    scale: [0, 1, 0],
                    y: [Math.random() * 100 + '%', Math.random() * 100 + '%']
                  }}
                  transition={{
                    duration: 2,
                    repeat: Infinity,
                    delay: i * 0.2
                  }}
                />
              ))}
            </motion.div>
          )}
        </AnimatePresence>
      </MagicCard>
    </motion.div>
  );
});

AdvancedMetricCard.displayName = 'AdvancedMetricCard';

// Componente de comparação de métricas
interface MetricComparisonProps {
  title: string;
  metrics: Array<{
    label: string;
    current: number;
    previous: number;
    color: string;
  }>;
  className?: string;
}

export const MetricComparison = memo(({
  title,
  metrics,
  className = ''
}: Readonly<MetricComparisonProps>) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      className={className}
    >
      <MagicCard className="p-6" gradientColor="#6366F1">
        <TextAnimate
          animation="blurInUp"
          className="text-lg font-semibold mb-6 text-gray-900 dark:text-white"
        >
          {title}
        </TextAnimate>

        <div className="space-y-4">
          {metrics.map((metric, index) => {
            const change = ((metric.current - metric.previous) / metric.previous) * 100;
            const isPositive = change > 0;

            return (
              <motion.div
                key={metric.label}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.1 }}
                className="flex items-center justify-between p-3 bg-gray-50 dark:bg-gray-800 rounded-lg"
              >
                <div className="flex items-center space-x-3">
                  <div
                    className="w-3 h-3 rounded-full"
                    style={{ backgroundColor: metric.color }}
                  />
                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300">
                    {metric.label}
                  </span>
                </div>

                <div className="flex items-center space-x-4">
                  <div className="text-right">
                    <div className="text-sm font-bold text-gray-900 dark:text-white">
                      {metric.current.toLocaleString()}
                    </div>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {metric.previous.toLocaleString()}
                    </div>
                  </div>

                  <div className={`text-xs font-medium px-2 py-1 rounded ${
                    isPositive 
                      ? 'text-green-700 bg-green-100 dark:text-green-400 dark:bg-green-900/30'
                      : 'text-red-700 bg-red-100 dark:text-red-400 dark:bg-red-900/30'
                  }`}>
                    {isPositive ? '+' : ''}{change.toFixed(1)}%
                  </div>
                </div>
              </motion.div>
            );
          })}
        </div>
      </MagicCard>
    </motion.div>
  );
});

MetricComparison.displayName = 'MetricComparison';

export { AdvancedMetricCard };
