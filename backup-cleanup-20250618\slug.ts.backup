/**
 * Utilitários para geração e validação de slugs
 * Implementado conforme especificado em CORREÇÃO-SLUG.md
 */

/**
 * Gera um slug a partir de um texto
 * @param texto - Texto para converter em slug
 * @returns Slug normalizado
 */
export function gerarSlug(texto: string): string {
  if (!texto || typeof texto !== 'string') {
    return '';
  }

  return texto
    .toLowerCase()
    .trim()
    // Remover acentos e caracteres especiais
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    // Substituir espaços e caracteres especiais por hífens
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    // Remover hífens múltiplos
    .replace(/-+/g, '-')
    // Remover hífens do início e fim
    .replace(/^-+|-+$/g, '');
}

/**
 * Valida se um slug está no formato correto
 * @param slug - Slug para validar
 * @returns true se o slug é válido
 */
export function validarSlug(slug: string): boolean {
  if (!slug || typeof slug !== 'string') {
    return false;
  }

  // Slug deve ter entre 3 e 100 caracteres
  if (slug.length < 3 || slug.length > 100) {
    return false;
  }

  // Slug deve conter apenas letras minúsculas, números e hífens
  // Não pode começar ou terminar com hífen
  const regexSlug = /^[a-z0-9]+(-[a-z0-9]+)*$/;
  return regexSlug.test(slug);
}

/**
 * Normaliza um slug existente
 * @param slug - Slug para normalizar
 * @returns Slug normalizado
 */
export function normalizarSlug(slug: string): string {
  if (!slug || typeof slug !== 'string') {
    return '';
  }

  return gerarSlug(slug);
}

/**
 * Gera sugestões de slug baseadas em um texto
 * @param texto - Texto base
 * @param sufixos - Array de sufixos para tentar (opcional)
 * @returns Array de sugestões de slug
 */
export function gerarSugestoesSlug(texto: string, sufixos: string[] = []): string[] {
  const slugBase = gerarSlug(texto);
  
  if (!slugBase) {
    return [];
  }

  const sugestoes: string[] = [slugBase];

  // Adicionar sugestões com sufixos
  sufixos.forEach(sufixo => {
    const slugComSufixo = gerarSlug(`${texto} ${sufixo}`);
    if (slugComSufixo && !sugestoes.includes(slugComSufixo)) {
      sugestoes.push(slugComSufixo);
    }
  });

  // Adicionar sugestões com números
  for (let i = 2; i <= 10; i++) {
    const slugComNumero = `${slugBase}-${i}`;
    if (!sugestoes.includes(slugComNumero)) {
      sugestoes.push(slugComNumero);
    }
  }

  return sugestoes.filter(validarSlug);
}

/**
 * Gera um slug único baseado em um texto e uma função de verificação
 * @param texto - Texto base
 * @param verificarExistencia - Função que verifica se o slug já existe
 * @returns Promise<string> - Slug único
 */
export async function gerarSlugUnico(
  texto: string,
  verificarExistencia: (slug: string) => Promise<boolean>
): Promise<string> {
  const slugBase = gerarSlug(texto);
  
  if (!slugBase) {
    throw new Error('Não foi possível gerar slug a partir do texto fornecido');
  }

  // Verificar se o slug base está disponível
  if (!(await verificarExistencia(slugBase))) {
    return slugBase;
  }

  // Tentar com números sequenciais
  for (let i = 2; i <= 100; i++) {
    const slugComNumero = `${slugBase}-${i}`;
    if (!(await verificarExistencia(slugComNumero))) {
      return slugComNumero;
    }
  }

  // Se chegou até aqui, gerar com timestamp
  const timestamp = Date.now().toString().slice(-6);
  const slugComTimestamp = `${slugBase}-${timestamp}`;
  
  return slugComTimestamp;
}

/**
 * Extrai informações de um slug
 * @param slug - Slug para analisar
 * @returns Objeto com informações do slug
 */
export function analisarSlug(slug: string): {
  valido: boolean;
  comprimento: number;
  temNumeros: boolean;
  temHifens: boolean;
  palavras: string[];
} {
  const valido = validarSlug(slug);
  const comprimento = slug ? slug.length : 0;
  const temNumeros = /\d/.test(slug);
  const temHifens = /-/.test(slug);
  const palavras = slug ? slug.split('-') : [];

  return {
    valido,
    comprimento,
    temNumeros,
    temHifens,
    palavras
  };
}

/**
 * Converte um slug de volta para texto legível
 * @param slug - Slug para converter
 * @returns Texto legível
 */
export function slugParaTexto(slug: string): string {
  if (!slug || typeof slug !== 'string') {
    return '';
  }

  return slug
    .split('-')
    .map(palavra => palavra.charAt(0).toUpperCase() + palavra.slice(1))
    .join(' ');
}

/**
 * Constantes para validação de slugs
 */
export const SLUG_CONFIG = {
  MIN_LENGTH: 3,
  MAX_LENGTH: 100,
  REGEX: /^[a-z0-9]+(-[a-z0-9]+)*$/,
  SUFIXOS_PADRAO: ['empresa', 'servicos', 'oficial', 'profissional', 'premium']
} as const;

/**
 * Tipos para TypeScript
 */
export interface SlugInfo {
  valido: boolean;
  comprimento: number;
  temNumeros: boolean;
  temHifens: boolean;
  palavras: string[];
}

export interface SlugOptions {
  maxLength?: number;
  sufixos?: string[];
  permitirNumeros?: boolean;
}
