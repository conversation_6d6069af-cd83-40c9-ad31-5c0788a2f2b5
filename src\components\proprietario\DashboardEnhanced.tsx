'use client';

import React, { memo, useState, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  ResponsiveProvider, 
  useResponsive, 
  ResponsiveContainer, 
  ResponsiveGrid,
  Responsive,
  usePerformanceOptimizations 
} from '@/components/ui/enhanced-responsive';
import { 
  AccessibilityProvider, 
  useAccessibilityEnhanced, 
  SkipLinks,
  useKeyboardShortcuts 
} from '@/components/ui/enhanced-accessibility';
import { EnhancedMagicCard, InteractiveMagicCard } from '@/components/ui/enhanced-magic-card';
import { EnhancedButton } from '@/components/ui/enhanced-button';
import { TextAnimate } from '@/components/ui/text-animate';
import { NumberTicker } from '@/components/ui/number-ticker';

// Componente de métricas responsivas
interface MetricCardProps {
  title: string;
  value: number | string;
  icon: string;
  trend?: 'up' | 'down' | 'stable';
  change?: number;
  format?: 'number' | 'currency' | 'percentage';
  delay?: number;
}

const MetricCard = memo(({ 
  title, 
  value, 
  icon, 
  trend, 
  change, 
  format = 'number',
  delay = 0 
}: MetricCardProps) => {
  const { breakpoint } = useResponsive();
  const { shouldReduceAnimations } = usePerformanceOptimizations();
  
  const formatValue = (val: number | string) => {
    if (typeof val === 'string') return val;
    
    switch (format) {
      case 'currency':
        return new Intl.NumberFormat('pt-BR', {
          style: 'currency',
          currency: 'BRL'
        }).format(val);
      case 'percentage':
        return `${val}%`;
      default:
        return val;
    }
  };

  const getTrendConfig = () => {
    switch (trend) {
      case 'up':
        return { icon: '📈', color: 'text-green-600', bgColor: 'bg-green-100' };
      case 'down':
        return { icon: '📉', color: 'text-red-600', bgColor: 'bg-red-100' };
      default:
        return { icon: '➡️', color: 'text-gray-600', bgColor: 'bg-gray-100' };
    }
  };

  const trendConfig = getTrendConfig();

  return (
    <motion.div
      initial={shouldReduceAnimations ? {} : { opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay, duration: shouldReduceAnimations ? 0 : 0.5 }}
    >
      <EnhancedMagicCard
        className="h-full"
        ariaLabel={`Métrica: ${title}`}
        focusable
        responsiveGradient
        disableEffectsOnMobile
      >
        <div className="space-y-4">
          {/* Header */}
          <div className="flex items-center justify-between">
            <motion.div
              className="text-2xl"
              whileHover={shouldReduceAnimations ? {} : { scale: 1.1, rotate: 5 }}
              transition={{ duration: 0.2 }}
            >
              {icon}
            </motion.div>
            
            {change !== undefined && (
              <div className={`flex items-center space-x-1 px-2 py-1 rounded-full text-xs ${trendConfig.bgColor} ${trendConfig.color}`}>
                <span>{trendConfig.icon}</span>
                <span>{change > 0 ? '+' : ''}{change}%</span>
              </div>
            )}
          </div>

          {/* Valor */}
          <div className="space-y-2">
            <div className={`font-bold text-gray-900 dark:text-white ${
              breakpoint === 'xs' ? 'text-xl' : 
              breakpoint === 'sm' ? 'text-2xl' : 'text-3xl'
            }`}>
              {typeof value === 'number' && format === 'number' ? (
                <NumberTicker value={value} />
              ) : (
                formatValue(value)
              )}
            </div>
            
            <div className={`text-gray-600 dark:text-gray-400 ${
              breakpoint === 'xs' ? 'text-xs' : 'text-sm'
            }`}>
              {title}
            </div>
          </div>
        </div>
      </EnhancedMagicCard>
    </motion.div>
  );
});

MetricCard.displayName = 'MetricCard';

// Componente de ação rápida
interface QuickActionProps {
  title: string;
  description: string;
  icon: string;
  onClick: () => void;
  variant?: 'primary' | 'secondary' | 'success' | 'danger';
  disabled?: boolean;
}

const QuickAction = memo(({ 
  title, 
  description, 
  icon, 
  onClick, 
  variant = 'primary',
  disabled = false 
}: QuickActionProps) => {
  const { announce } = useAccessibilityEnhanced();
  
  const handleClick = useCallback(() => {
    announce(`Executando ação: ${title}`);
    onClick();
  }, [announce, title, onClick]);

  return (
    <InteractiveMagicCard
      className="h-full cursor-pointer"
      onClick={handleClick}
      ariaLabel={`Ação rápida: ${title}`}
      role="button"
    >
      <div className="space-y-4">
        <div className="text-3xl">{icon}</div>
        <div className="space-y-2">
          <h3 className="font-semibold text-gray-900 dark:text-white">
            {title}
          </h3>
          <p className="text-sm text-gray-600 dark:text-gray-400">
            {description}
          </p>
        </div>
        <EnhancedButton
          variant={variant}
          size="sm"
          fullWidth
          disabled={disabled}
          ariaLabel={`Executar ${title}`}
        >
          Executar
        </EnhancedButton>
      </div>
    </InteractiveMagicCard>
  );
});

QuickAction.displayName = 'QuickAction';

// Componente principal do dashboard
interface DashboardEnhancedProps {
  className?: string;
}

const DashboardEnhancedComponent = memo(({ 
  className = '' 
}: DashboardEnhancedProps) => {
  const [activeSection, setActiveSection] = useState<string | null>(null);
  const { breakpoint, isTouch } = useResponsive();
  const { state: accessibilityState, announce } = useAccessibilityEnhanced();
  const { shouldReduceAnimations } = usePerformanceOptimizations();

  // Configurar atalhos de teclado
  useKeyboardShortcuts({
    'alt+1': () => {
      document.getElementById('main-content')?.focus();
      announce('Navegando para conteúdo principal');
    },
    'alt+2': () => {
      document.getElementById('metrics-section')?.focus();
      announce('Navegando para seção de métricas');
    },
    'alt+3': () => {
      document.getElementById('actions-section')?.focus();
      announce('Navegando para ações rápidas');
    },
    'ctrl+shift+a': () => {
      announce('Painel de acessibilidade aberto');
    }
  });

  // Dados de exemplo
  const metrics = [
    {
      title: 'Receita Mensal',
      value: 15750,
      icon: '💰',
      trend: 'up' as const,
      change: 12.5,
      format: 'currency' as const
    },
    {
      title: 'Agendamentos',
      value: 234,
      icon: '📅',
      trend: 'up' as const,
      change: 8.3,
      format: 'number' as const
    },
    {
      title: 'Taxa de Conversão',
      value: 68.5,
      icon: '🎯',
      trend: 'down' as const,
      change: -2.1,
      format: 'percentage' as const
    },
    {
      title: 'Satisfação',
      value: 94.2,
      icon: '⭐',
      trend: 'up' as const,
      change: 1.8,
      format: 'percentage' as const
    }
  ];

  const quickActions = [
    {
      title: 'Novo Agendamento',
      description: 'Criar um novo agendamento para cliente',
      icon: '📅',
      onClick: () => announce('Criando novo agendamento'),
      variant: 'primary' as const
    },
    {
      title: 'Relatório Mensal',
      description: 'Gerar relatório de performance do mês',
      icon: '📊',
      onClick: () => announce('Gerando relatório mensal'),
      variant: 'secondary' as const
    },
    {
      title: 'Backup Dados',
      description: 'Fazer backup dos dados do sistema',
      icon: '💾',
      onClick: () => announce('Iniciando backup'),
      variant: 'success' as const
    },
    {
      title: 'Configurações',
      description: 'Acessar configurações do sistema',
      icon: '⚙️',
      onClick: () => announce('Abrindo configurações'),
      variant: 'secondary' as const
    }
  ];

  return (
    <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ${className}`}>
      {/* Skip Links */}
      <SkipLinks />

      <ResponsiveContainer maxWidth="2xl" className="py-8">
        {/* Header */}
        <motion.header
          className="mb-8"
          initial={shouldReduceAnimations ? {} : { opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: shouldReduceAnimations ? 0 : 0.6 }}
        >
          <div className="text-center space-y-4">
            <TextAnimate
              animation="blurInUp"
              className={`font-bold text-gray-900 dark:text-white ${
                breakpoint === 'xs' ? 'text-2xl' : 
                breakpoint === 'sm' ? 'text-3xl' : 'text-4xl'
              }`}
              by="word"
            >
              🚀 Dashboard Responsivo e Acessível
            </TextAnimate>
            
            <motion.p
              className={`text-gray-600 dark:text-gray-400 max-w-2xl mx-auto ${
                breakpoint === 'xs' ? 'text-sm' : 'text-lg'
              }`}
              initial={shouldReduceAnimations ? {} : { opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: shouldReduceAnimations ? 0 : 0.3, duration: shouldReduceAnimations ? 0 : 0.6 }}
            >
              Sistema avançado com responsividade completa e acessibilidade WCAG 2.1 AA
            </motion.p>

            {/* Indicadores de status */}
            <motion.div
              className="flex items-center justify-center space-x-6 flex-wrap gap-2"
              initial={shouldReduceAnimations ? {} : { opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: shouldReduceAnimations ? 0 : 0.5, duration: shouldReduceAnimations ? 0 : 0.6 }}
            >
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-green-500 rounded-full animate-pulse"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">Sistema Online</span>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-blue-500 rounded-full"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  Breakpoint: {breakpoint}
                </span>
              </div>
              
              <div className="flex items-center space-x-2">
                <div className="w-3 h-3 bg-purple-500 rounded-full"></div>
                <span className="text-sm text-gray-600 dark:text-gray-400">
                  {isTouch ? 'Touch' : 'Desktop'}
                </span>
              </div>
            </motion.div>
          </div>
        </motion.header>

        {/* Conteúdo Principal */}
        <main id="main-content" tabIndex={-1} className="space-y-8">
          {/* Seção de Métricas */}
          <section id="metrics-section" tabIndex={-1}>
            <motion.div
              className="mb-6"
              initial={shouldReduceAnimations ? {} : { opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: shouldReduceAnimations ? 0 : 0.7, duration: shouldReduceAnimations ? 0 : 0.6 }}
            >
              <h2 className={`font-semibold text-gray-900 dark:text-white ${
                breakpoint === 'xs' ? 'text-lg' : 'text-2xl'
              }`}>
                📊 Métricas de Negócio
              </h2>
              <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
                Indicadores de performance em tempo real
              </p>
            </motion.div>

            <ResponsiveGrid
              columns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4, '2xl': 4 }}
              gap={{ xs: 4, sm: 4, md: 6, lg: 6, xl: 8, '2xl': 8 }}
            >
              {metrics.map((metric, index) => (
                <MetricCard
                  key={metric.title}
                  {...metric}
                  delay={shouldReduceAnimations ? 0 : index * 0.1}
                />
              ))}
            </ResponsiveGrid>
          </section>

          {/* Seção de Ações Rápidas */}
          <section id="actions-section" tabIndex={-1}>
            <motion.div
              className="mb-6"
              initial={shouldReduceAnimations ? {} : { opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: shouldReduceAnimations ? 0 : 1.0, duration: shouldReduceAnimations ? 0 : 0.6 }}
            >
              <h2 className={`font-semibold text-gray-900 dark:text-white ${
                breakpoint === 'xs' ? 'text-lg' : 'text-2xl'
              }`}>
                ⚡ Ações Rápidas
              </h2>
              <p className="text-gray-600 dark:text-gray-400 text-sm mt-1">
                Funcionalidades mais utilizadas
              </p>
            </motion.div>

            <ResponsiveGrid
              columns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4, '2xl': 4 }}
              gap={{ xs: 4, sm: 4, md: 6, lg: 6, xl: 8, '2xl': 8 }}
            >
              {quickActions.map((action, index) => (
                <motion.div
                  key={action.title}
                  initial={shouldReduceAnimations ? {} : { opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: shouldReduceAnimations ? 0 : 1.2 + index * 0.1, duration: shouldReduceAnimations ? 0 : 0.5 }}
                >
                  <QuickAction {...action} />
                </motion.div>
              ))}
            </ResponsiveGrid>
          </section>

          {/* Informações de Acessibilidade */}
          <Responsive above="md">
            <motion.section
              className="mt-12"
              initial={shouldReduceAnimations ? {} : { opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: shouldReduceAnimations ? 0 : 1.5, duration: shouldReduceAnimations ? 0 : 0.6 }}
            >
              <EnhancedMagicCard className="p-6">
                <div className="text-center space-y-4">
                  <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                    ♿ Recursos de Acessibilidade Ativados
                  </h3>
                  <div className="grid grid-cols-1 md:grid-cols-3 gap-4 text-sm text-gray-600 dark:text-gray-400">
                    <div>✅ Navegação por teclado (Alt+1, Alt+2, Alt+3)</div>
                    <div>✅ Leitores de tela otimizados</div>
                    <div>✅ Alto contraste disponível</div>
                    <div>✅ Movimento reduzido respeitado</div>
                    <div>✅ Touch targets otimizados</div>
                    <div>✅ WCAG 2.1 AA compliant</div>
                  </div>
                </div>
              </EnhancedMagicCard>
            </motion.section>
          </Responsive>
        </main>
      </ResponsiveContainer>
    </div>
  );
});

DashboardEnhancedComponent.displayName = 'DashboardEnhanced';

// Wrapper com providers
export const DashboardEnhanced = memo(() => {
  return (
    <ResponsiveProvider>
      <AccessibilityProvider>
        <DashboardEnhancedComponent />
      </AccessibilityProvider>
    </ResponsiveProvider>
  );
});

DashboardEnhanced.displayName = 'DashboardEnhanced';
