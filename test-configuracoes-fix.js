/**
 * Script para testar se a correção da página de configurações funcionou
 * Testa o login e acesso à página de configurações
 */

const { createClient } = require('@supabase/supabase-js');

// Configuração do Supabase
const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://tlbpsdgoklkekoxzmzlo.supabase.co';
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

if (!supabaseKey) {
  console.error('❌ NEXT_PUBLIC_SUPABASE_ANON_KEY não encontrada');
  process.exit(1);
}

const supabase = createClient(supabaseUrl, supabaseKey);

// Credenciais de teste
const TEST_EMAIL = '<EMAIL>';
const TEST_PASSWORD = 'senha123';

async function testConfiguracoesFix() {
  console.log('🧪 Testando correção da página de configurações');
  console.log('=' .repeat(60));

  try {
    // 1. Login
    console.log('\n1️⃣ Fazendo login como Maria Santos...');
    const { data: loginData, error: loginError } = await supabase.auth.signInWithPassword({
      email: TEST_EMAIL,
      password: TEST_PASSWORD
    });

    if (loginError) {
      console.log('❌ Erro no login:', loginError.message);
      return;
    }

    console.log('✅ Login bem-sucedido!');
    console.log(`   User ID: ${loginData.user.id}`);
    console.log(`   Role: ${loginData.user.user_metadata?.role}`);

    // 2. Testar API que o hook useEmpresaProprietario usa
    console.log('\n2️⃣ Testando API do dashboard (usada pelo hook)...');
    
    const { data: { session } } = await supabase.auth.getSession();
    
    if (!session) {
      console.log('❌ Sessão não encontrada');
      return;
    }

    // Simular chamada para a API que o hook usa
    const fetch = (await import('node-fetch')).default;
    
    const response = await fetch('http://localhost:3001/api/proprietario/dashboard/empresa', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
        'Cookie': `sb-tlbpsdgoklkekoxzmzlo-auth-token=${session.access_token}`
      }
    });

    const result = await response.json();

    if (!response.ok) {
      console.log('❌ Erro na API do dashboard:', result.error);
      console.log('   Status:', response.status);
      return;
    }

    console.log('✅ API do dashboard funcionando!');
    console.log('   Empresa encontrada:', result.data?.empresa?.nome_empresa || 'N/A');
    console.log('   Status da empresa:', result.data?.empresa?.status || 'N/A');

    // 3. Testar API antiga (que estava causando problema)
    console.log('\n3️⃣ Testando API antiga (para comparação)...');
    
    const responseOld = await fetch('http://localhost:3001/api/empresas/proprietario', {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${session.access_token}`,
        'Content-Type': 'application/json',
        'Cookie': `sb-tlbpsdgoklkekoxzmzlo-auth-token=${session.access_token}`
      }
    });

    const resultOld = await responseOld.json();

    if (!responseOld.ok) {
      console.log('⚠️ API antiga ainda com problema:', resultOld.error);
      console.log('   Status:', responseOld.status);
      console.log('   (Isso é esperado - por isso fizemos a correção)');
    } else {
      console.log('✅ API antiga também funcionando:', resultOld.empresa?.nome_empresa || 'N/A');
    }

    // 4. Logout
    console.log('\n4️⃣ Fazendo logout...');
    await supabase.auth.signOut();
    console.log('✅ Logout realizado!');

    // Resumo
    console.log('\n' + '=' .repeat(60));
    console.log('📊 RESULTADO DO TESTE');
    console.log('=' .repeat(60));
    console.log('✅ Login: OK');
    console.log('✅ API Dashboard (hook): OK');
    console.log('✅ Logout: OK');
    console.log('\n🎉 Correção da página de configurações FUNCIONOU!');
    console.log('\n📝 A página agora usa o hook useEmpresaProprietario que funciona corretamente.');

  } catch (error) {
    console.error('\n💥 Erro durante o teste:', error.message);
    
    if (error.code === 'ECONNREFUSED') {
      console.log('\n⚠️ Servidor não está rodando. Execute: npm run dev');
    }
  }
}

// Executar teste
testConfiguracoesFix();
