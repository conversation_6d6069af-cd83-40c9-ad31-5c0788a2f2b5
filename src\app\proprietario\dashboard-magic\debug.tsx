'use client';

import React, { useState, useEffect } from 'react';
import { useAuth } from '@/contexts/AuthContext';
import { useEmpresaProprietario } from '@/hooks/useEmpresaProprietario';

export default function DashboardDebugPage() {
  const { user, loading: authLoading, initialized } = useAuth();
  const { 
    empresa, 
    loading: empresaLoading, 
    error: empresaError,
    temEmpresa 
  } = useEmpresaProprietario();

  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
  }, []);

  if (!mounted) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <p>Inicializando...</p>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 p-8">
      <div className="max-w-4xl mx-auto">
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6 mb-6">
          <h1 className="text-3xl font-bold text-gray-900 dark:text-white mb-6">
            🔧 Dashboard ServiceTech - Debug
          </h1>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Estado da Autenticação */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                🔐 Autenticação
              </h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                  <span>Loading:</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    authLoading ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                  }`}>
                    {authLoading ? 'true' : 'false'}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                  <span>Initialized:</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    initialized ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {initialized ? 'true' : 'false'}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                  <span>User:</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    user ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {user ? user.email : 'null'}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                  <span>Role:</span>
                  <span className="px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800">
                    {user?.role || 'none'}
                  </span>
                </div>
              </div>
            </div>

            {/* Estado da Empresa */}
            <div className="space-y-4">
              <h3 className="text-lg font-semibold text-gray-900 dark:text-white">
                🏢 Empresa
              </h3>
              <div className="space-y-3 text-sm">
                <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                  <span>Loading:</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    empresaLoading ? 'bg-yellow-100 text-yellow-800' : 'bg-green-100 text-green-800'
                  }`}>
                    {empresaLoading ? 'true' : 'false'}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                  <span>Tem Empresa:</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    temEmpresa ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
                  }`}>
                    {temEmpresa ? 'true' : 'false'}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                  <span>Nome:</span>
                  <span className="px-2 py-1 rounded text-xs font-medium bg-blue-100 text-blue-800 truncate max-w-32">
                    {empresa?.nome_empresa || 'N/A'}
                  </span>
                </div>
                <div className="flex justify-between items-center p-3 bg-gray-50 dark:bg-gray-700 rounded">
                  <span>Status:</span>
                  <span className={`px-2 py-1 rounded text-xs font-medium ${
                    empresa?.status === 'ativo' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
                  }`}>
                    {empresa?.status || 'N/A'}
                  </span>
                </div>
              </div>
            </div>
          </div>

          {/* Erros */}
          {empresaError && (
            <div className="mt-6 p-4 bg-red-50 border border-red-200 rounded-lg">
              <h4 className="font-semibold text-red-800 mb-2">❌ Erro da Empresa:</h4>
              <code className="text-sm text-red-700 break-all">
                {empresaError}
              </code>
            </div>
          )}

          {/* Estado do Sistema */}
          <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
            <h4 className="font-semibold text-blue-800 mb-2">ℹ️ Informações do Sistema:</h4>
            <div className="grid grid-cols-2 gap-4 text-sm">
              <div>
                <span className="font-medium">Environment:</span> {process.env.NODE_ENV}
              </div>
              <div>
                <span className="font-medium">Timestamp:</span> {new Date().toLocaleTimeString()}
              </div>
              <div>
                <span className="font-medium">URL:</span> {typeof window !== 'undefined' ? window.location.pathname : 'SSR'}
              </div>
              <div>
                <span className="font-medium">Mounted:</span> {mounted ? 'true' : 'false'}
              </div>
            </div>
          </div>

          {/* Ações */}
          <div className="mt-6 flex flex-wrap gap-3">
            <button
              onClick={() => window.location.reload()}
              className="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              🔄 Recarregar
            </button>
            <button
              onClick={() => window.location.href = '/proprietario/dashboard-magic'}
              className="bg-green-600 hover:bg-green-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              🚀 Dashboard Normal
            </button>
            <button
              onClick={() => window.location.href = '/proprietario/dashboard'}
              className="bg-purple-600 hover:bg-purple-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              📊 Dashboard Antigo
            </button>
            <button
              onClick={() => window.location.href = '/login'}
              className="bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              🔐 Login
            </button>
          </div>
        </div>

        {/* Teste de Componentes Simples */}
        <div className="bg-white dark:bg-gray-800 rounded-lg shadow-lg p-6">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
            🧪 Teste de Componentes
          </h3>
          
          {/* Teste básico de renderização */}
          <div className="space-y-4">
            <div className="p-4 border border-gray-200 rounded-lg">
              <h4 className="font-medium mb-2">Teste Básico:</h4>
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white p-4 rounded">
                ✅ Componente renderizando corretamente
              </div>
            </div>

            {/* Informações da empresa se disponível */}
            {temEmpresa && empresa && (
              <div className="p-4 border border-green-200 bg-green-50 rounded-lg">
                <h4 className="font-medium mb-2 text-green-800">✅ Dados da Empresa:</h4>
                <div className="text-sm text-green-700">
                  <p><strong>Nome:</strong> {empresa.nome_empresa}</p>
                  <p><strong>Status:</strong> {empresa.status}</p>
                  <p><strong>Segmento:</strong> {empresa.segmento || 'Não definido'}</p>
                  <p><strong>ID:</strong> {empresa.empresa_id}</p>
                </div>
              </div>
            )}

            {/* Aviso se não tem empresa */}
            {!empresaLoading && !temEmpresa && (
              <div className="p-4 border border-yellow-200 bg-yellow-50 rounded-lg">
                <h4 className="font-medium mb-2 text-yellow-800">⚠️ Empresa não encontrada</h4>
                <p className="text-sm text-yellow-700">
                  Você precisa criar uma empresa para acessar o dashboard.
                </p>
                <button
                  onClick={() => window.location.href = '/onboarding'}
                  className="mt-2 bg-yellow-600 hover:bg-yellow-700 text-white font-medium py-1 px-3 rounded text-sm transition-colors"
                >
                  Criar Empresa
                </button>
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
