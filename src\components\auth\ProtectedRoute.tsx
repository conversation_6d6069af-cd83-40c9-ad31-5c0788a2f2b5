'use client';

import React, { memo, useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useAuth } from '@/contexts/AuthContext';
import { useAuthRecovery } from '@/hooks/useAuthRecovery';

interface ProtectedRouteProps {
  children: React.ReactNode;
  requiredRole?: string | string[];
  fallbackUrl?: string;
  showLoading?: boolean;
}

const ProtectedRoute = memo(function ProtectedRoute({
  children,
  requiredRole,
  fallbackUrl = '/login',
  showLoading = true
}: ProtectedRouteProps) {
  const { user, loading, initialized } = useAuth();
  const { isRecovering, recoveryAttempts } = useAuthRecovery();
  const router = useRouter();
  const [timeoutReached, setTimeoutReached] = React.useState(false);

  // Timeout para evitar loading infinito
  React.useEffect(() => {
    const timeout = setTimeout(() => {
      setTimeoutReached(true);
    }, 10000); // 10 segundos

    return () => clearTimeout(timeout);
  }, []);

  // Se passou do timeout e ainda está carregando, mostrar erro
  if (timeoutReached && (loading || !initialized)) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="text-center max-w-md mx-auto p-8">
          <div className="text-6xl mb-6">⚠️</div>
          <h2 className="text-2xl font-bold text-gray-900 dark:text-white mb-4">
            Problema de Autenticação
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-6">
            A verificação de autenticação está demorando mais que o esperado.
          </p>
          <div className="space-y-3">
            <button
              onClick={() => window.location.reload()}
              className="w-full bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              Recarregar Página
            </button>
            <button
              onClick={() => router.push('/login')}
              className="w-full bg-gray-600 hover:bg-gray-700 text-white font-medium py-2 px-4 rounded-lg transition-colors"
            >
              Ir para Login
            </button>
          </div>

          {/* Debug info */}
          <div className="mt-6 p-3 bg-gray-100 dark:bg-gray-800 rounded text-xs text-left">
            <p>Loading: {loading ? 'true' : 'false'}</p>
            <p>Initialized: {initialized ? 'true' : 'false'}</p>
            <p>Recovering: {isRecovering ? 'true' : 'false'}</p>
            <p>Recovery Attempts: {recoveryAttempts}</p>
          </div>
        </div>
      </div>
    );
  }

  // Mostrar loading enquanto verifica autenticação ou durante recuperação
  if (loading || !initialized || isRecovering) {
    if (!showLoading) return null;

    return (
      <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800">
        <div className="text-center">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
            {isRecovering ? 'Recuperando sessão...' : 'Verificando autenticação...'}
          </h2>
          <p className="text-gray-600 dark:text-gray-400 mb-4">
            {isRecovering
              ? `Tentativa ${recoveryAttempts} de recuperação da sessão.`
              : 'Aguarde um momento.'
            }
          </p>

          {/* Barra de progresso */}
          <div className="w-64 bg-gray-200 dark:bg-gray-700 rounded-full h-2 mx-auto">
            <div className="bg-blue-600 h-2 rounded-full animate-pulse" style={{ width: '70%' }}></div>
          </div>

          {/* Contador de tempo */}
          <p className="text-sm text-gray-500 dark:text-gray-400 mt-4">
            {Math.floor((10000 - (timeoutReached ? 10000 : 0)) / 1000)}s restantes
          </p>
        </div>
      </div>
    );
  }

  // Redirecionar se não estiver autenticado
  if (!user) {
    router.push(fallbackUrl);
    return null;
  }

  // Verificar papel se especificado
  if (requiredRole) {
    const hasRequiredRole = Array.isArray(requiredRole) 
      ? requiredRole.includes(user.role)
      : user.role === requiredRole;

    if (!hasRequiredRole) {
      router.push('/acesso-negado');
      return null;
    }
  }

  return <>{children}</>;
});

export { ProtectedRoute };

// Hook para verificar permissões
export function usePermissions() {
  const { user } = useAuth();

  const hasRole = (role: string | string[]) => {
    if (!user) return false;
    
    if (Array.isArray(role)) {
      return role.includes(user.role);
    }
    
    return user.role === role;
  };

  const isAdmin = () => hasRole('Administrador');
  const isOwner = () => hasRole('Proprietario');
  const isCollaborator = () => hasRole('Colaborador');
  const isUser = () => hasRole('Usuario');
  
  const canManageEstablishment = () => hasRole(['Administrador', 'Proprietario']);
  const canManageSchedules = () => hasRole(['Administrador', 'Proprietario', 'Colaborador']);
  const canViewReports = () => hasRole(['Administrador', 'Proprietario']);

  return {
    user,
    hasRole,
    isAdmin,
    isOwner,
    isCollaborator,
    isUser,
    canManageEstablishment,
    canManageSchedules,
    canViewReports,
  };
}

// Componente para renderizar conteúdo baseado em papel
interface RoleBasedRenderProps {
  allowedRoles: string | string[];
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

const RoleBasedRender = memo(function RoleBasedRender({ allowedRoles, children, fallback = null }: RoleBasedRenderProps) {
  const { hasRole } = usePermissions();
  
  if (hasRole(allowedRoles)) {
    return <>{children}</>;
  }
  
  return <>{fallback}</>;
});

export { RoleBasedRender };

// HOC para proteger componentes
export function withAuth<P extends object>(
  Component: React.ComponentType<P>,
  requiredRole?: string | string[]
) {
  return function AuthenticatedComponent(props: P) {
    return (
      <ProtectedRoute requiredRole={requiredRole}>
        <Component {...props} />
      </ProtectedRoute>
    );
  };
}
