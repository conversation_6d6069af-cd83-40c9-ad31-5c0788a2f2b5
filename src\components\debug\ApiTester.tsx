'use client';

import React, { useState } from 'react';
import { motion } from 'framer-motion';

interface ApiTesterProps {
  className?: string;
}

export function ApiTester({ className = '' }: ApiTesterProps) {
  const [result, setResult] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testApi = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('🧪 Testando API /api/proprietario/dashboard/empresa...');
      
      const response = await fetch('/api/proprietario/dashboard/empresa', {
        method: 'GET',
        credentials: 'include',
        headers: {
          'Content-Type': 'application/json'
        }
      });

      console.log('📡 Resposta da API:', {
        status: response.status,
        statusText: response.statusText,
        ok: response.ok,
        headers: Object.fromEntries(response.headers.entries())
      });

      const data = await response.json();
      
      console.log('📊 Dados recebidos:', data);

      if (!response.ok) {
        throw new Error(`API Error: ${response.status} - ${data.error || response.statusText}`);
      }

      setResult(data);
    } catch (err: any) {
      console.error('❌ Erro no teste da API:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const testAuth = async () => {
    setLoading(true);
    setError(null);
    setResult(null);

    try {
      console.log('🔐 Testando autenticação...');
      
      // Primeiro, vamos verificar se há um usuário logado
      const authResponse = await fetch('/api/auth/user', {
        method: 'GET',
        credentials: 'include'
      });

      const authData = await authResponse.json();
      console.log('👤 Dados de autenticação:', authData);

      setResult({ auth: authData });
    } catch (err: any) {
      console.error('❌ Erro no teste de autenticação:', err);
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className={`bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 rounded-lg p-6 ${className}`}>
      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4">
        🧪 Testador de API
      </h3>

      <div className="space-y-4">
        <div className="flex space-x-4">
          <button
            onClick={testAuth}
            disabled={loading}
            className="bg-green-500 hover:bg-green-600 disabled:bg-gray-400 text-white px-4 py-2 rounded text-sm font-medium"
          >
            {loading ? 'Testando...' : 'Testar Autenticação'}
          </button>
          
          <button
            onClick={testApi}
            disabled={loading}
            className="bg-blue-500 hover:bg-blue-600 disabled:bg-gray-400 text-white px-4 py-2 rounded text-sm font-medium"
          >
            {loading ? 'Testando...' : 'Testar API Dashboard'}
          </button>
        </div>

        {loading && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex items-center space-x-2 text-blue-600"
          >
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-600"></div>
            <span>Executando teste...</span>
          </motion.div>
        )}

        {error && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-red-50 border border-red-200 rounded-lg p-4"
          >
            <h4 className="font-medium text-red-800 mb-2">❌ Erro no Teste</h4>
            <p className="text-sm text-red-700">{error}</p>
          </motion.div>
        )}

        {result && (
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            className="bg-green-50 border border-green-200 rounded-lg p-4"
          >
            <h4 className="font-medium text-green-800 mb-2">✅ Resultado do Teste</h4>
            <div className="text-sm text-green-700">
              <pre className="whitespace-pre-wrap overflow-auto max-h-96 bg-white p-3 rounded border">
                {JSON.stringify(result, null, 2)}
              </pre>
            </div>
          </motion.div>
        )}

        <div className="text-xs text-gray-500 dark:text-gray-400">
          <p>💡 <strong>Dica:</strong> Abra o console do navegador (F12) para ver logs detalhados dos testes.</p>
        </div>
      </div>
    </div>
  );
}
