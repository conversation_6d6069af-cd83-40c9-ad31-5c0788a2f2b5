'use client';

import React, { memo } from 'react';
import { TextAnimate } from '@/components/ui/text-animate';
import { VisualEffectsMinimal } from '@/components/ui/visual-effects-minimal';
import { useResponsiveConfig, PerformanceOptimizer } from '@/components/ui/responsive-config';
import { InformacoesEmpresaMagic } from './InformacoesEmpresaMagic';
import { MetricasNegocioMagic } from './MetricasNegocioMagic';
import { StatusPlanoSaasMagic } from './StatusPlanoSaasMagic';
import { AlertasDashboardMagic } from './AlertasDashboardMagic';
import { AcoesRapidasMagic } from './AcoesRapidasMagic';
import { motion } from 'framer-motion';
import { AccessibilityPanel, AccessibilityButton } from '@/components/ui/accessibility-panel';
import { DashboardDebug } from '@/components/debug/DashboardDebug';
import { EmpresaSetupGuide } from './EmpresaSetupGuide';
import { DashboardErrorWrapper } from '@/components/ui/DashboardErrorBoundary';
import { DashboardNotifications } from '@/components/ui/DashboardNotifications';
import { useEmpresaProprietario } from '@/hooks/useEmpresaProprietario';
import { DashboardLayoutManager } from '@/components/ui/dashboard-layout-manager';
import { SectionAction, SectionMetric } from '@/components/ui/dashboard-sections-advanced';

interface DashboardMagicLayoutProps {
  className?: string;
}

const DashboardMagicLayoutComponent = ({ className = '' }: Readonly<DashboardMagicLayoutProps>) => {
  const {
    isMobile,
    isDesktop,
    prefersReducedMotion
  } = useResponsiveConfig();

  const [showAccessibilityPanel, setShowAccessibilityPanel] = React.useState(false);

  // Verificar se há empresa cadastrada
  const { temEmpresa, loading: loadingEmpresa } = useEmpresaProprietario();

  // Configurar ações das seções
  const sectionActions: Record<string, SectionAction[]> = {
    'informacoes-empresa': [
      {
        id: 'edit-empresa',
        label: 'Editar',
        icon: '✏️',
        variant: 'primary',
        onClick: () => console.log('Editar empresa')
      }
    ],
    'metricas-negocio': [
      {
        id: 'relatorio',
        label: 'Relatório',
        icon: '📈',
        variant: 'primary',
        onClick: () => console.log('Gerar relatório')
      },
      {
        id: 'exportar',
        label: 'Exportar',
        icon: '💾',
        variant: 'secondary',
        onClick: () => console.log('Exportar dados')
      }
    ],
    'status-plano-saas': [
      {
        id: 'upgrade',
        label: 'Upgrade',
        icon: '⬆️',
        variant: 'success',
        onClick: () => console.log('Fazer upgrade')
      }
    ],
    'alertas-dashboard': [
      {
        id: 'marcar-lidos',
        label: 'Marcar Lidos',
        icon: '✅',
        variant: 'secondary',
        onClick: () => console.log('Marcar todos como lidos')
      }
    ],
    'acoes-rapidas': [
      {
        id: 'personalizar',
        label: 'Personalizar',
        icon: '⚙️',
        variant: 'secondary',
        onClick: () => console.log('Personalizar ações')
      }
    ]
  };

  // Configurar métricas das seções (exemplo)
  const sectionMetrics: Record<string, SectionMetric[]> = {
    'metricas-negocio': [
      {
        label: 'Agendamentos Hoje',
        value: 12,
        change: 15.5,
        trend: 'up',
        format: 'number',
        icon: '📅'
      },
      {
        label: 'Receita Mensal',
        value: 5000,
        change: 8.2,
        trend: 'up',
        format: 'currency',
        icon: '💰'
      },
      {
        label: 'Clientes Ativos',
        value: 45,
        change: -2.1,
        trend: 'down',
        format: 'number',
        icon: '👥'
      },
      {
        label: 'Taxa de Satisfação',
        value: 94.5,
        change: 3.2,
        trend: 'up',
        format: 'percentage',
        icon: '⭐'
      }
    ]
  };

  // Função para refresh das seções
  const handleSectionRefresh = async (sectionId: string) => {
    console.log(`Atualizando seção: ${sectionId}`);
    // Simular delay de atualização
    await new Promise(resolve => setTimeout(resolve, 1000));
  };

  // Componentes das seções
  const sectionComponents = {
    'informacoes-empresa': <InformacoesEmpresaMagic />,
    'metricas-negocio': <MetricasNegocioMagic />,
    'status-plano-saas': <StatusPlanoSaasMagic />,
    'alertas-dashboard': <AlertasDashboardMagic />,
    'acoes-rapidas': <AcoesRapidasMagic />
  };

  // Classes CSS simplificadas
  const classes = {
    padding: isMobile ? 'p-4' : 'p-6',
    grid: isMobile ? 'grid-cols-1' : 'grid-cols-6',
    header: isMobile ? 'text-2xl' : 'text-4xl',
    text: isMobile ? 'text-base' : 'text-lg',
    colSpan: isMobile ? 'col-span-1' : 'col-span-3'
  };



  // Se está carregando, mostrar loading
  if (loadingEmpresa) {
    return (
      <PerformanceOptimizer>
        <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ${classes.padding} ${className}`}>
          <div className="flex items-center justify-center min-h-screen">
            <div className="text-center">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
              <h2 className="text-xl font-semibold text-gray-900 dark:text-white mb-2">
                Carregando Dashboard
              </h2>
              <p className="text-gray-600 dark:text-gray-400">
                Verificando configuração da empresa...
              </p>
            </div>
          </div>
        </div>
      </PerformanceOptimizer>
    );
  }

  // Se não tem empresa, mostrar guia de configuração
  if (!temEmpresa) {
    return (
      <PerformanceOptimizer>
        <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ${classes.padding} ${className}`}>
          <VisualEffectsMinimal
            showScrollProgress={true}
            showGradients={!prefersReducedMotion}
          />

          {/* Header */}
          <motion.div
            className="mb-8 relative z-10"
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <TextAnimate
              animation={prefersReducedMotion ? "fadeIn" : "blurInUp"}
              className={`${classes.header} font-bold text-gray-900 dark:text-white mb-2`}
              by="word"
            >
              Dashboard do Proprietário
            </TextAnimate>
            <motion.p
              className={`text-gray-600 dark:text-gray-400 ${classes.text}`}
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3, duration: 0.6 }}
            >
              Configure sua empresa para começar
            </motion.p>
          </motion.div>

          {/* Guia de Configuração */}
          <EmpresaSetupGuide className="relative z-10" />

          {/* Debug temporário - apenas em desenvolvimento */}
          {process.env.NODE_ENV === 'development' && <DashboardDebug />}
        </div>
      </PerformanceOptimizer>
    );
  }

  return (
    <DashboardErrorWrapper>
      <PerformanceOptimizer>
        <div className={`min-h-screen bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-900 dark:to-gray-800 ${classes.padding} ${className}`}>
        {/* Efeitos visuais de fundo */}
        <VisualEffectsMinimal
          showScrollProgress={true}
          showGradients={!prefersReducedMotion}
        />

      {/* Header da Dashboard */}
      <motion.div
        className="mb-8 relative z-10"
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
      >
        <TextAnimate
          animation={prefersReducedMotion ? "fadeIn" : "blurInUp"}
          className={`${classes.header} font-bold text-gray-900 dark:text-white mb-2`}
          by="word"
        >
          Dashboard do Proprietário
        </TextAnimate>
        <motion.p
          className={`text-gray-600 dark:text-gray-400 ${classes.text}`}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.3, duration: 0.6 }}
        >
          Gerencie seu negócio com eficiência e estilo
        </motion.p>
      </motion.div>

      {/* Dashboard com Layout Manager Avançado */}
      <DashboardLayoutManager
        sectionActions={sectionActions}
        sectionMetrics={sectionMetrics}
        onSectionRefresh={handleSectionRefresh}
        className="relative z-10"
      >
        {sectionComponents}
      </DashboardLayoutManager>



      {/* Indicador de scroll - apenas em desktop */}
      {isDesktop && (
        <motion.div
          className="fixed bottom-6 right-6 w-12 h-12 bg-blue-500 rounded-full flex items-center justify-center text-white shadow-lg cursor-pointer z-50"
          whileHover={{ scale: prefersReducedMotion ? 1 : 1.1 }}
          whileTap={{ scale: prefersReducedMotion ? 1 : 0.9 }}
          onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 1.0, duration: 0.6 }}
        >
          <motion.div
            animate={prefersReducedMotion ? {} : { y: [-2, 2, -2] }}
            transition={{ duration: 2, repeat: Infinity }}
          >
            ↑
          </motion.div>
        </motion.div>
      )}

      {/* Botão de Acessibilidade */}
      <AccessibilityButton
        onClick={() => setShowAccessibilityPanel(true)}
      />

      {/* Painel de Acessibilidade */}
      <AccessibilityPanel
        isOpen={showAccessibilityPanel}
        onClose={() => setShowAccessibilityPanel(false)}
      />

      {/* Debug temporário - apenas em desenvolvimento */}
      {process.env.NODE_ENV === 'development' && <DashboardDebug />}

      {/* Sistema de notificações */}
      <DashboardNotifications />

      </div>
      </PerformanceOptimizer>
    </DashboardErrorWrapper>
  );
};

// Memoizar componente para evitar re-renders desnecessários
export const DashboardMagicLayout = memo(DashboardMagicLayoutComponent, (prevProps, nextProps) => {
  return prevProps.className === nextProps.className;
});
