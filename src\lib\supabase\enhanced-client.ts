'use client';

import { createBrowserClient, SupabaseClient } from '@supabase/ssr';
import { Database } from '@/types/supabase';

// Configurações avançadas do cliente
interface EnhancedClientConfig {
  enableRetry?: boolean;
  maxRetries?: number;
  retryDelay?: number;
  enableMetrics?: boolean;
  enableLogging?: boolean;
  connectionPooling?: boolean;
  queryTimeout?: number;
  enableCompression?: boolean;
}

// Métricas de performance
interface SupabaseMetrics {
  totalQueries: number;
  successfulQueries: number;
  failedQueries: number;
  averageResponseTime: number;
  cacheHitRate: number;
  connectionErrors: number;
  lastError?: string;
  lastErrorTime?: Date;
}

// Cache de queries
interface QueryCache {
  [key: string]: {
    data: any;
    timestamp: number;
    ttl: number;
    hits: number;
  };
}

class EnhancedSupabaseClient {
  private client: SupabaseClient<Database>;
  private config: Required<EnhancedClientConfig>;
  private metrics: SupabaseMetrics;
  private queryCache: QueryCache = {};
  private connectionPool: Map<string, SupabaseClient> = new Map();
  private retryQueue: Array<() => Promise<any>> = [];
  private isProcessingQueue = false;

  constructor(config: EnhancedClientConfig = {}) {
    this.config = {
      enableRetry: true,
      maxRetries: 3,
      retryDelay: 1000,
      enableMetrics: true,
      enableLogging: process.env.NODE_ENV === 'development',
      connectionPooling: true,
      queryTimeout: 30000,
      enableCompression: true,
      ...config
    };

    this.metrics = {
      totalQueries: 0,
      successfulQueries: 0,
      failedQueries: 0,
      averageResponseTime: 0,
      cacheHitRate: 0,
      connectionErrors: 0
    };

    this.client = this.createOptimizedClient();
    this.setupPerformanceMonitoring();
    this.startCacheCleanup();
  }

  private createOptimizedClient(): SupabaseClient<Database> {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
    const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;

    if (!supabaseUrl || !supabaseAnonKey) {
      throw new Error('Missing Supabase URL or Anon Key');
    }

    return createBrowserClient<Database>(supabaseUrl, supabaseAnonKey, {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true,
        flowType: 'pkce',
        storage: typeof window !== 'undefined' ? window.localStorage : undefined,
        storageKey: 'servicetech-auth-token'
      },
      global: {
        headers: {
          'X-Client-Info': 'servicetech-enhanced',
          'X-Client-Version': process.env.NEXT_PUBLIC_APP_VERSION || '1.0.0',
          ...(this.config.enableCompression && {
            'Accept-Encoding': 'gzip, deflate, br'
          })
        }
      },
      db: {
        schema: 'public'
      },
      realtime: {
        params: {
          eventsPerSecond: 10
        }
      }
    });
  }

  private setupPerformanceMonitoring() {
    if (!this.config.enableMetrics) return;

    // Interceptar requests para coletar métricas
    const originalRequest = this.client.rest.fetch;
    this.client.rest.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const startTime = performance.now();
      this.metrics.totalQueries++;

      try {
        const response = await originalRequest.call(this.client.rest, input, init);
        const endTime = performance.now();
        const responseTime = endTime - startTime;

        this.metrics.successfulQueries++;
        this.updateAverageResponseTime(responseTime);

        if (this.config.enableLogging) {
          console.log(`[Supabase] Query completed in ${responseTime.toFixed(2)}ms`);
        }

        return response;
      } catch (error) {
        this.metrics.failedQueries++;
        this.metrics.lastError = error instanceof Error ? error.message : 'Unknown error';
        this.metrics.lastErrorTime = new Date();

        if (this.config.enableLogging) {
          console.error('[Supabase] Query failed:', error);
        }

        throw error;
      }
    };
  }

  private updateAverageResponseTime(responseTime: number) {
    const totalTime = this.metrics.averageResponseTime * (this.metrics.successfulQueries - 1);
    this.metrics.averageResponseTime = (totalTime + responseTime) / this.metrics.successfulQueries;
  }

  private startCacheCleanup() {
    setInterval(() => {
      const now = Date.now();
      let removedCount = 0;

      Object.keys(this.queryCache).forEach(key => {
        const cached = this.queryCache[key];
        if (now - cached.timestamp > cached.ttl) {
          delete this.queryCache[key];
          removedCount++;
        }
      });

      if (this.config.enableLogging && removedCount > 0) {
        console.log(`[Supabase Cache] Cleaned up ${removedCount} expired entries`);
      }
    }, 60000); // Cleanup a cada minuto
  }

  // Query com cache inteligente
  async query<T = any>(
    table: string,
    options: {
      select?: string;
      filters?: Record<string, any>;
      orderBy?: Array<{ column: string; ascending?: boolean }>;
      limit?: number;
      offset?: number;
      cache?: boolean;
      cacheTTL?: number;
    } = {}
  ): Promise<{ data: T[] | null; error: any; fromCache: boolean }> {
    const {
      select = '*',
      filters = {},
      orderBy = [],
      limit,
      offset,
      cache = true,
      cacheTTL = 5 * 60 * 1000 // 5 minutos
    } = options;

    // Gerar chave de cache
    const cacheKey = this.generateCacheKey(table, { select, filters, orderBy, limit, offset });

    // Verificar cache
    if (cache && this.queryCache[cacheKey]) {
      const cached = this.queryCache[cacheKey];
      if (Date.now() - cached.timestamp < cached.ttl) {
        cached.hits++;
        this.updateCacheHitRate();
        
        if (this.config.enableLogging) {
          console.log(`[Supabase Cache] Cache hit for ${table}`);
        }
        
        return { data: cached.data, error: null, fromCache: true };
      }
    }

    // Executar query
    try {
      let query = this.client.from(table).select(select);

      // Aplicar filtros
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            query = query.in(key, value);
          } else if (typeof value === 'object' && value.operator) {
            this.applyOperator(query, key, value);
          } else {
            query = query.eq(key, value);
          }
        }
      });

      // Aplicar ordenação
      orderBy.forEach(({ column, ascending = true }) => {
        query = query.order(column, { ascending });
      });

      // Aplicar paginação
      if (limit) {
        query = query.limit(limit);
      }
      if (offset) {
        query = query.range(offset, offset + (limit || 10) - 1);
      }

      const { data, error } = await query;

      // Armazenar no cache
      if (cache && !error && data) {
        this.queryCache[cacheKey] = {
          data,
          timestamp: Date.now(),
          ttl: cacheTTL,
          hits: 0
        };
      }

      return { data, error, fromCache: false };
    } catch (error) {
      if (this.config.enableRetry) {
        return this.retryQuery(() => this.query(table, options));
      }
      throw error;
    }
  }

  private applyOperator(query: any, key: string, value: any) {
    switch (value.operator) {
      case 'gte':
        return query.gte(key, value.value);
      case 'lte':
        return query.lte(key, value.value);
      case 'gt':
        return query.gt(key, value.value);
      case 'lt':
        return query.lt(key, value.value);
      case 'like':
        return query.like(key, value.value);
      case 'ilike':
        return query.ilike(key, value.value);
      case 'neq':
        return query.neq(key, value.value);
      case 'in':
        return query.in(key, value.value);
      case 'contains':
        return query.contains(key, value.value);
      case 'containedBy':
        return query.containedBy(key, value.value);
      default:
        return query.eq(key, value.value);
    }
  }

  // Mutação com retry automático
  async mutate<T = any>(
    table: string,
    operation: 'insert' | 'update' | 'delete' | 'upsert',
    data: any,
    options: {
      returning?: string;
      onConflict?: string;
      ignoreDuplicates?: boolean;
    } = {}
  ): Promise<{ data: T | null; error: any }> {
    const { returning = '*', onConflict, ignoreDuplicates = false } = options;

    try {
      let query: any;

      switch (operation) {
        case 'insert':
          query = this.client.from(table).insert(data);
          if (ignoreDuplicates) {
            query = query.select(returning);
          }
          break;

        case 'update':
          const { id, ...updateData } = data;
          query = this.client
            .from(table)
            .update(updateData)
            .eq('id', id)
            .select(returning);
          break;

        case 'delete':
          query = this.client
            .from(table)
            .delete()
            .eq('id', data.id)
            .select(returning);
          break;

        case 'upsert':
          query = this.client.from(table).upsert(data);
          if (onConflict) {
            query = query.onConflict(onConflict);
          }
          query = query.select(returning);
          break;

        default:
          throw new Error(`Operação não suportada: ${operation}`);
      }

      const result = await query;

      // Invalidar cache relacionado
      this.invalidateTableCache(table);

      return result;
    } catch (error) {
      if (this.config.enableRetry) {
        return this.retryQuery(() => this.mutate(table, operation, data, options));
      }
      throw error;
    }
  }

  // Sistema de retry inteligente
  private async retryQuery<T>(queryFn: () => Promise<T>): Promise<T> {
    let lastError: any;

    for (let attempt = 1; attempt <= this.config.maxRetries; attempt++) {
      try {
        return await queryFn();
      } catch (error) {
        lastError = error;
        
        if (attempt === this.config.maxRetries) {
          break;
        }

        // Delay exponencial
        const delay = this.config.retryDelay * Math.pow(2, attempt - 1);
        await new Promise(resolve => setTimeout(resolve, delay));

        if (this.config.enableLogging) {
          console.warn(`[Supabase] Retry attempt ${attempt}/${this.config.maxRetries} after ${delay}ms`);
        }
      }
    }

    throw lastError;
  }

  // Subscription otimizada
  subscribe(
    table: string,
    options: {
      event?: 'INSERT' | 'UPDATE' | 'DELETE' | '*';
      filter?: string;
      schema?: string;
    } = {}
  ) {
    const { event = '*', filter, schema = 'public' } = options;

    return this.client
      .channel(`${table}-${Date.now()}`)
      .on(
        'postgres_changes',
        {
          event,
          schema,
          table,
          filter
        },
        (payload) => {
          // Invalidar cache relacionado quando há mudanças
          this.invalidateTableCache(table);
          
          if (this.config.enableLogging) {
            console.log(`[Supabase Realtime] ${event} on ${table}:`, payload);
          }
        }
      );
  }

  // Gerenciamento de cache
  private generateCacheKey(table: string, options: any): string {
    return `${table}-${JSON.stringify(options)}`;
  }

  private invalidateTableCache(table: string) {
    Object.keys(this.queryCache).forEach(key => {
      if (key.startsWith(`${table}-`)) {
        delete this.queryCache[key];
      }
    });
  }

  private updateCacheHitRate() {
    const totalCacheRequests = Object.values(this.queryCache).reduce((sum, cache) => sum + cache.hits, 0);
    const totalRequests = this.metrics.totalQueries;
    this.metrics.cacheHitRate = totalRequests > 0 ? (totalCacheRequests / totalRequests) * 100 : 0;
  }

  // Métodos públicos para gerenciamento
  getMetrics(): SupabaseMetrics {
    return { ...this.metrics };
  }

  clearCache(table?: string) {
    if (table) {
      this.invalidateTableCache(table);
    } else {
      this.queryCache = {};
    }
  }

  getClient(): SupabaseClient<Database> {
    return this.client;
  }

  // Cleanup
  destroy() {
    this.queryCache = {};
    this.connectionPool.clear();
    this.retryQueue = [];
  }
}

// Instância singleton
let enhancedClient: EnhancedSupabaseClient | null = null;

export function getEnhancedSupabaseClient(config?: EnhancedClientConfig): EnhancedSupabaseClient {
  if (!enhancedClient) {
    enhancedClient = new EnhancedSupabaseClient(config);
  }
  return enhancedClient;
}

export { EnhancedSupabaseClient };
export type { EnhancedClientConfig, SupabaseMetrics };
